// Default minimum and maximum height for collapsed panel headers.
$header-collapsed-height: 48px !default;
$header-collapsed-minimum-height: 36px !default;
$header-collapsed-maximum-height:
    $header-collapsed-height !default;

// Default minimum and maximum height for expanded panel headers.
$header-expanded-height: 64px !default;
$header-expanded-minimum-height: 48px !default;
$header-expanded-maximum-height:
    $header-expanded-height !default;

// Density configuration for the expansion panel. Captures the
// height for both expanded and collapsed panel headers.
$header-density-config: (
  collapsed-height: (
    default: $header-collapsed-height,
    maximum: $header-collapsed-maximum-height,
    minimum: $header-collapsed-minimum-height,
  ),
  expanded-height: (
    default: $header-expanded-height,
    maximum: $header-expanded-maximum-height,
    minimum: $header-expanded-minimum-height,
  )
) !default;

// Note: Keep this in sync with the animation timing for the toggle indicator
// and body expansion. These are animated using Angular animations.
$header-transition: 225ms cubic-bezier(0.4, 0, 0.2, 1);
