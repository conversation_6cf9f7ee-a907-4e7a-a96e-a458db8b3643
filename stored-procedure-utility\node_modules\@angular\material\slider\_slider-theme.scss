@use 'sass:map';
@use '@material/slider/slider-theme' as mdc-slider-theme;
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/typography/typography';
@use '../core/style/sass-utils';
@use '../core/tokens/token-utils';
@use '../core/tokens/m2/mat/slider' as tokens-mat-slider;
@use '../core/tokens/m2/mdc/slider' as tokens-mdc-slider;

@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {
    // Add default values for tokens not related to color, typography, or density.
    @include sass-utils.current-selector-or-root() {
      @include mdc-slider-theme.theme(tokens-mdc-slider.get-unthemable-tokens());
    }
  }
}

@mixin color($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color));
  }
  @else {
    $is-dark: inspection.get-theme-type($theme) == dark;

    $mdc-color-tokens: token-utils.resolve-elevation(
        tokens-mdc-slider.get-color-tokens($theme),
        handle-elevation,
        handle-shadow-color
    );

    $mat-slider-color-tokens: tokens-mat-slider.get-color-tokens($theme);

  // Add values for MDC slider tokens.
  @include sass-utils.current-selector-or-root() {
    @include mdc-slider-theme.theme($mdc-color-tokens);
    @include _slider-ripple-color($theme, primary);
    @include token-utils.create-token-values(
      tokens-mat-slider.$prefix,
      $mat-slider-color-tokens
    );

    .mat-accent {
      @include mdc-slider-theme.theme(
        tokens-mdc-slider.private-get-color-palette-color-tokens($theme, accent));
      @include _slider-ripple-color($theme, accent);
    }

      .mat-warn {
        @include mdc-slider-theme.theme(
            tokens-mdc-slider.private-get-color-palette-color-tokens($theme, warn));
        @include _slider-ripple-color($theme, warn);
      }
    }
  }
}

@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  }
  @else {
    // Add values for MDC slider tokens.
    @include sass-utils.current-selector-or-root() {
      @include mdc-slider-theme.theme(tokens-mdc-slider.get-typography-tokens($theme));
    }
  }
}

@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  }
  @else {
    // Add values for MDC slider tokens.
    @include sass-utils.current-selector-or-root() {
      @include mdc-slider-theme.theme(tokens-mdc-slider.get-density-tokens($theme));
    }
  }
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-slider') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme));
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

// Generated MDC ripple color tokens are not being calculated so needs to be set
@mixin _slider-ripple-color($theme, $palette-name) {
  $color: inspection.get-theme-color($theme, $palette-name);

  --mat-mdc-slider-ripple-color: #{$color};
  --mat-mdc-slider-hover-ripple-color: #{rgba($color, 0.05)};
  --mat-mdc-slider-focus-ripple-color: #{rgba($color, 0.2)};
}

@mixin _theme-from-tokens($tokens) {
  @if ($tokens != ()) {
    @include mdc-slider-theme.theme(map.get($tokens, tokens-mdc-slider.$prefix));
    @include token-utils.create-token-values(
        tokens-mat-slider.$prefix, map.get($tokens, tokens-mat-slider.$prefix));
  }
}
