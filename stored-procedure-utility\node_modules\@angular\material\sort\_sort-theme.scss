@use '../core/tokens/m2/mat/sort' as tokens-mat-sort;
@use '../core/style/sass-utils';
@use '../core/typography/typography';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/tokens/token-utils';

@mixin base($theme) {}

@mixin color($theme) {
  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(tokens-mat-sort.$prefix,
      tokens-mat-sort.get-color-tokens($theme));
  }
}

@mixin typography($theme) {
  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(tokens-mat-sort.$prefix,
      tokens-mat-sort.get-typography-tokens($theme));
  }
}

@mixin density($theme) {
  $density-scale: inspection.get-theme-density($theme);

  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(tokens-mat-sort.$prefix,
      tokens-mat-sort.get-density-tokens($theme));
  }
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-sort') {
    @include base($theme);
    @if inspection.theme-has($theme, color) {
      @include color($theme);
    }
    @if inspection.theme-has($theme, density) {
      @include density($theme);
    }
    @if inspection.theme-has($theme, typography) {
      @include typography($theme);
    }
  }
}
