import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { 
  StoredProcedure, 
  ExcelData, 
  ParameterMapping, 
  ExecutionSummary, 
  ExecutionResult 
} from '../../models/stored-procedure.model';
import { StoredProcedureService } from '../../services/stored-procedure.service';
import { ExcelService } from '../../services/excel.service';

@Component({
  selector: 'app-execution',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatTableModule,
    MatChipsModule,
    MatSnackBarModule,
    MatDialogModule
  ],
  templateUrl: './execution.component.html',
  styleUrls: ['./execution.component.css']
})
export class ExecutionComponent implements OnChanges {
  @Input() selectedProcedure: StoredProcedure | null = null;
  @Input() excelData: ExcelData | null = null;
  @Input() mappings: ParameterMapping[] = [];

  isExecuting: boolean = false;
  executionSummary: ExecutionSummary | null = null;
  canExecute: boolean = false;
  validationErrors: string[] = [];

  displayedColumns: string[] = ['row', 'status', 'message', 'executionTime'];

  constructor(
    private procedureService: StoredProcedureService,
    private excelService: ExcelService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    this.validateExecution();
  }

  private validateExecution(): void {
    this.validationErrors = [];
    this.canExecute = false;

    // Check if procedure is selected
    if (!this.selectedProcedure) {
      this.validationErrors.push('No stored procedure selected');
      return;
    }

    // Check if Excel data is loaded
    if (!this.excelData || this.excelData.totalRows === 0) {
      this.validationErrors.push('No Excel data loaded');
      return;
    }

    // Check if mappings are configured
    if (this.mappings.length === 0) {
      this.validationErrors.push('No parameter mappings configured');
      return;
    }

    // Validate mappings
    const requiredParams = this.mappings.filter(m => 
      !m.parameter.isNullable && !m.parameter.defaultValue
    );

    for (const mapping of requiredParams) {
      if (mapping.mappingType === 'none') {
        this.validationErrors.push(`Required parameter '${mapping.parameter.name}' is not mapped`);
      } else if (mapping.mappingType === 'static' && !mapping.staticValue?.trim()) {
        this.validationErrors.push(`Required parameter '${mapping.parameter.name}' has no static value`);
      } else if (mapping.mappingType === 'column' && !mapping.excelColumn) {
        this.validationErrors.push(`Parameter '${mapping.parameter.name}' has no column selected`);
      }
    }

    // Check for column mappings that reference non-existent columns
    const columnMappings = this.mappings.filter(m => m.mappingType === 'column');
    for (const mapping of columnMappings) {
      if (mapping.excelColumn && !this.excelData.columns.includes(mapping.excelColumn)) {
        this.validationErrors.push(`Parameter '${mapping.parameter.name}' references invalid column`);
      }
    }

    this.canExecute = this.validationErrors.length === 0;
  }

  async executeStoredProcedure(): Promise<void> {
    if (!this.canExecute || !this.selectedProcedure || !this.excelData) {
      return;
    }

    this.isExecuting = true;
    this.executionSummary = null;

    try {
      this.procedureService.executeProcedureForRows(
        this.selectedProcedure.id,
        this.mappings,
        this.excelData.rows
      ).subscribe({
        next: (summary) => {
          this.executionSummary = summary;
          this.isExecuting = false;
          this.showExecutionComplete(summary);
        },
        error: (error) => {
          console.error('Execution error:', error);
          this.isExecuting = false;
          this.snackBar.open(
            'Error executing stored procedure. Please check the console for details.',
            'Close',
            { 
              duration: 5000,
              panelClass: ['error-snackbar']
            }
          );
        }
      });
    } catch (error) {
      console.error('Execution error:', error);
      this.isExecuting = false;
      this.snackBar.open(
        'Unexpected error during execution',
        'Close',
        { 
          duration: 5000,
          panelClass: ['error-snackbar']
        }
      );
    }
  }

  private showExecutionComplete(summary: ExecutionSummary): void {
    const message = `Execution completed! ${summary.successCount} successful, ${summary.errorCount} failed out of ${summary.totalRows} rows.`;
    
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: summary.errorCount > 0 ? ['warning-snackbar'] : ['success-snackbar']
    });
  }

  exportResults(): void {
    if (!this.executionSummary) return;

    const exportData = this.executionSummary.results.map((result, index) => ({
      'Row Number': result.rowIndex + 1,
      'Status': result.success ? 'Success' : 'Error',
      'Message': result.message || result.error || '',
      'Execution Time (ms)': result.executionTime.toFixed(2),
      'Result': result.result ? JSON.stringify(result.result) : ''
    }));

    const fileName = `execution_results_${this.selectedProcedure?.name}_${new Date().toISOString().split('T')[0]}`;
    this.excelService.exportResultsToExcel(exportData, fileName);
    
    this.snackBar.open('Results exported to Excel', 'Close', { duration: 3000 });
  }

  getStatusIcon(result: ExecutionResult): string {
    return result.success ? 'check_circle' : 'error';
  }

  getStatusColor(result: ExecutionResult): string {
    return result.success ? 'primary' : 'warn';
  }

  getExecutionProgress(): number {
    if (!this.executionSummary) return 0;
    return (this.executionSummary.results.length / this.executionSummary.totalRows) * 100;
  }

  clearResults(): void {
    this.executionSummary = null;
  }

  getValidationSummary(): string {
    if (this.canExecute) {
      return `Ready to execute for ${this.excelData?.totalRows || 0} rows`;
    } else {
      return `${this.validationErrors.length} validation error(s)`;
    }
  }

  getMappedParametersCount(): number {
    return this.mappings.filter(m => m.mappingType !== 'none').length;
  }
}
