/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from '@angular/core';
/**
 * Used to provide a table to some of the sub-components without causing a circular dependency.
 * @docs-private
 */
export const CDK_TABLE = new InjectionToken('CDK_TABLE');
/** Injection token that can be used to specify the text column options. */
export const TEXT_COLUMN_OPTIONS = new InjectionToken('text-column-options');
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidG9rZW5zLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vc3JjL2Nkay90YWJsZS90b2tlbnMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsT0FBTyxFQUFDLGNBQWMsRUFBQyxNQUFNLGVBQWUsQ0FBQztBQUU3Qzs7O0dBR0c7QUFDSCxNQUFNLENBQUMsTUFBTSxTQUFTLEdBQUcsSUFBSSxjQUFjLENBQU0sV0FBVyxDQUFDLENBQUM7QUFjOUQsMkVBQTJFO0FBQzNFLE1BQU0sQ0FBQyxNQUFNLG1CQUFtQixHQUFHLElBQUksY0FBYyxDQUNuRCxxQkFBcUIsQ0FDdEIsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5pbXBvcnQge0luamVjdGlvblRva2VufSBmcm9tICdAYW5ndWxhci9jb3JlJztcblxuLyoqXG4gKiBVc2VkIHRvIHByb3ZpZGUgYSB0YWJsZSB0byBzb21lIG9mIHRoZSBzdWItY29tcG9uZW50cyB3aXRob3V0IGNhdXNpbmcgYSBjaXJjdWxhciBkZXBlbmRlbmN5LlxuICogQGRvY3MtcHJpdmF0ZVxuICovXG5leHBvcnQgY29uc3QgQ0RLX1RBQkxFID0gbmV3IEluamVjdGlvblRva2VuPGFueT4oJ0NES19UQUJMRScpO1xuXG4vKiogQ29uZmlndXJhYmxlIG9wdGlvbnMgZm9yIGBDZGtUZXh0Q29sdW1uYC4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgVGV4dENvbHVtbk9wdGlvbnM8VD4ge1xuICAvKipcbiAgICogRGVmYXVsdCBmdW5jdGlvbiB0aGF0IHByb3ZpZGVzIHRoZSBoZWFkZXIgdGV4dCBiYXNlZCBvbiB0aGUgY29sdW1uIG5hbWUgaWYgYSBoZWFkZXJcbiAgICogdGV4dCBpcyBub3QgcHJvdmlkZWQuXG4gICAqL1xuICBkZWZhdWx0SGVhZGVyVGV4dFRyYW5zZm9ybT86IChuYW1lOiBzdHJpbmcpID0+IHN0cmluZztcblxuICAvKiogRGVmYXVsdCBkYXRhIGFjY2Vzc29yIHRvIHVzZSBpZiBvbmUgaXMgbm90IHByb3ZpZGVkLiAqL1xuICBkZWZhdWx0RGF0YUFjY2Vzc29yPzogKGRhdGE6IFQsIG5hbWU6IHN0cmluZykgPT4gc3RyaW5nO1xufVxuXG4vKiogSW5qZWN0aW9uIHRva2VuIHRoYXQgY2FuIGJlIHVzZWQgdG8gc3BlY2lmeSB0aGUgdGV4dCBjb2x1bW4gb3B0aW9ucy4gKi9cbmV4cG9ydCBjb25zdCBURVhUX0NPTFVNTl9PUFRJT05TID0gbmV3IEluamVjdGlvblRva2VuPFRleHRDb2x1bW5PcHRpb25zPGFueT4+KFxuICAndGV4dC1jb2x1bW4tb3B0aW9ucycsXG4pO1xuIl19