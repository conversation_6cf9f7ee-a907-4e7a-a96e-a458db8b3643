@use '../core/tokens/m2/mat/table' as tokens-mat-table;
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/typography/typography';
@use '../core/tokens/token-utils';
@use '../core/style/sass-utils';

@mixin base($theme) {
  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(
        tokens-mat-table.$prefix, tokens-mat-table.get-unthemable-tokens());
  }
}

@mixin color($theme) {
  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(tokens-mat-table.$prefix,
      tokens-mat-table.get-color-tokens($theme));
  }
}

@mixin typography($theme) {
  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(tokens-mat-table.$prefix,
      tokens-mat-table.get-typography-tokens($theme));
  }
}

@mixin density($theme) {
  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values(tokens-mat-table.$prefix,
      tokens-mat-table.get-density-tokens($theme));
  }
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-table') {
    @include base($theme);
    @if inspection.theme-has($theme, color) {
      @include color($theme);
    }
    @if inspection.theme-has($theme, density) {
      @include density($theme);
    }
    @if inspection.theme-has($theme, typography) {
      @include typography($theme);
    }
  }
}
