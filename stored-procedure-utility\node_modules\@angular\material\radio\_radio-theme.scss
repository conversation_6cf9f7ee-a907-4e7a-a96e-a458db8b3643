@use 'sass:map';
@use '@material/radio/radio-theme' as mdc-radio-theme;
@use '@material/form-field' as mdc-form-field;
@use '../core/mdc-helpers/mdc-helpers';
@use '../core/style/sass-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/tokens/token-utils';
@use '../core/typography/typography';
@use '../core/tokens/m2/mdc/radio' as tokens-mdc-radio;
@use '../core/tokens/m2/mat/radio' as tokens-mat-radio;

@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-radio-theme.theme(tokens-mdc-radio.get-unthemable-tokens());
      @include token-utils.create-token-values(
          tokens-mat-radio.$prefix, tokens-mat-radio.get-unthemable-tokens());
    }
  }
}

@mixin color($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color));
  }
  @else {
    @include mdc-helpers.using-mdc-theme($theme) {
      .mat-mdc-radio-button {
        @include mdc-form-field.core-styles($query: mdc-helpers.$mdc-theme-styles-query);
      }
    }

    .mat-mdc-radio-button {
      &.mat-primary {
        @include mdc-radio-theme.theme(tokens-mdc-radio.get-color-tokens($theme, primary));
        @include token-utils.create-token-values(tokens-mat-radio.$prefix,
          tokens-mat-radio.get-color-tokens($theme, primary));
      }

      &.mat-accent {
        @include mdc-radio-theme.theme(tokens-mdc-radio.get-color-tokens($theme));
        @include token-utils.create-token-values(tokens-mat-radio.$prefix,
          tokens-mat-radio.get-color-tokens($theme));
      }

      &.mat-warn {
        @include mdc-radio-theme.theme(tokens-mdc-radio.get-color-tokens($theme, warn));
        @include token-utils.create-token-values(tokens-mat-radio.$prefix,
          tokens-mat-radio.get-color-tokens($theme, warn));
      }
    }
  }
}

@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-radio-theme.theme(tokens-mdc-radio.get-typography-tokens($theme));
    }

    .mat-mdc-radio-button {
      @include mdc-helpers.using-mdc-typography($theme) {
        @include mdc-form-field.core-styles($query: mdc-helpers.$mdc-typography-styles-query);
      }
    }
  }
}

@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  }
  @else {
    $density-scale: inspection.get-theme-density($theme);

    @include sass-utils.current-selector-or-root() {
      @include mdc-radio-theme.theme(tokens-mdc-radio.get-density-tokens($theme));
    }

    @include mdc-helpers.if-touch-targets-unsupported($density-scale) {
      .mat-mdc-radio-touch-target {
        display: none;
      }
    }
  }
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-radio') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme));
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens) {
  @if ($tokens != ()) {
    @include mdc-radio-theme.theme(map.get($tokens, tokens-mdc-radio.$prefix));
    @include token-utils.create-token-values(
        tokens-mat-radio.$prefix, map.get($tokens, tokens-mat-radio.$prefix));
  }
}
