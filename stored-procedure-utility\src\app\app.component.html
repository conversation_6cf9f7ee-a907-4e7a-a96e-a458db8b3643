<!-- Main Application Layout -->
<div class="app-container">
  <!-- Header -->
  <mat-toolbar color="primary" class="app-header">
    <mat-icon class="app-icon">storage</mat-icon>
    <span class="app-title">{{title}}</span>
    <span class="spacer"></span>
    <button mat-icon-button>
      <mat-icon>help</mat-icon>
    </button>
  </mat-toolbar>

  <!-- Main Content -->
  <div class="app-content">
    <!-- Step 1: Search and Select Stored Procedure -->
    <app-procedure-search 
      (procedureSelected)="onProcedureSelected($event)">
    </app-procedure-search>

    <!-- Step 2: View Procedure Parameters -->
    <app-procedure-parameters 
      [selectedProcedure]="selectedProcedure"
      (parametersLoaded)="onParametersLoaded($event)">
    </app-procedure-parameters>

    <!-- Step 3: Upload Excel File -->
    <app-excel-upload 
      (excelDataLoaded)="onExcelDataLoaded($event)">
    </app-excel-upload>

    <!-- Step 4: Map Columns to Parameters -->
    <app-column-mapping 
      [parameters]="procedureParameters"
      [excelData]="excelData"
      (mappingsChanged)="onMappingsChanged($event)">
    </app-column-mapping>

    <!-- Step 5: Execute Procedure -->
    <app-execution 
      [selectedProcedure]="selectedProcedure"
      [excelData]="excelData"
      [mappings]="parameterMappings">
    </app-execution>
  </div>

  <!-- Footer -->
  <div class="app-footer">
    <p>&copy; 2024 Stored Procedure Utility. Built with Angular and Angular Material.</p>
  </div>
</div>

<router-outlet></router-outlet>
