.search-card {
  margin: 20px;
  max-width: 1200px;
}

.search-container {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.search-field {
  flex: 1;
  min-width: 300px;
}

.search-button, .clear-button {
  height: 56px;
  min-width: 120px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  text-align: center;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.results-container {
  margin-top: 20px;
}

.results-container h3 {
  margin-bottom: 16px;
  color: #333;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  text-align: center;
  color: #666;
}

.no-results mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
}

.procedures-table {
  width: 100%;
  margin-top: 16px;
}

.procedures-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.procedures-table td {
  padding: 12px 8px;
}

.selected-row {
  background-color: #e3f2fd !important;
}

.selected-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px;
  background-color: #e8f5e8;
  border-radius: 4px;
  color: #2e7d32;
}

.selected-info mat-icon {
  color: #4caf50;
}

button.selected {
  background-color: #4caf50 !important;
  color: white !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .search-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-field {
    min-width: unset;
  }
  
  .search-button, .clear-button {
    width: 100%;
  }
  
  .procedures-table {
    font-size: 14px;
  }
  
  .procedures-table td, .procedures-table th {
    padding: 8px 4px;
  }
}
