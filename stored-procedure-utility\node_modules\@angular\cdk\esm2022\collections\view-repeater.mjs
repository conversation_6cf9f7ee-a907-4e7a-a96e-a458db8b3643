/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken, } from '@angular/core';
/**
 * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.
 * @docs-private
 */
export const _VIEW_REPEATER_STRATEGY = new InjectionToken('_ViewRepeater');
//# sourceMappingURL=data:application/json;base64,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