{"version": 3, "sources": ["../../../../../node_modules/@angular/material/fesm2022/snack-bar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, inject, ViewChild, Injector, TemplateRef, Injectable, Optional, SkipSelf, NgModule } from '@angular/core';\nimport { CommonModule, DOCUMENT } from '@angular/common';\nimport * as i2 from '@angular/material/button';\nimport { MatButtonModule } from '@angular/material/button';\nimport { Subject } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3 from '@angular/cdk/portal';\nimport { BasePortalOutlet, CdkPortalOutlet, PortalModule, ComponentPortal, TemplatePortal } from '@angular/cdk/portal';\nimport * as i1 from '@angular/cdk/platform';\nimport { take, takeUntil } from 'rxjs/operators';\nimport * as i2$1 from '@angular/cdk/a11y';\nimport * as i3$1 from '@angular/cdk/layout';\nimport { Breakpoints } from '@angular/cdk/layout';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nfunction SimpleSnackBar_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function SimpleSnackBar_Conditional_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.action());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.data.action, \" \");\n  }\n}\nconst _c0 = [\"label\"];\nfunction MatSnackBarContainer_ng_template_4_Template(rf, ctx) {}\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n  constructor(containerInstance, _overlayRef) {\n    this._overlayRef = _overlayRef;\n    /** Subject for notifying the user that the snack bar has been dismissed. */\n    this._afterDismissed = new Subject();\n    /** Subject for notifying the user that the snack bar has opened and appeared. */\n    this._afterOpened = new Subject();\n    /** Subject for notifying the user that the snack bar action was called. */\n    this._onAction = new Subject();\n    /** Whether the snack bar was dismissed using the action button. */\n    this._dismissedByAction = false;\n    this.containerInstance = containerInstance;\n    containerInstance._onExit.subscribe(() => this._finishDismiss());\n  }\n  /** Dismisses the snack bar. */\n  dismiss() {\n    if (!this._afterDismissed.closed) {\n      this.containerInstance.exit();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /** Marks the snackbar action clicked. */\n  dismissWithAction() {\n    if (!this._onAction.closed) {\n      this._dismissedByAction = true;\n      this._onAction.next();\n      this._onAction.complete();\n      this.dismiss();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /**\n   * Marks the snackbar action clicked.\n   * @deprecated Use `dismissWithAction` instead.\n   * @breaking-change 8.0.0\n   */\n  closeWithAction() {\n    this.dismissWithAction();\n  }\n  /** Dismisses the snack bar after some duration */\n  _dismissAfter(duration) {\n    // Note that we need to cap the duration to the maximum value for setTimeout, because\n    // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n    this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n  }\n  /** Marks the snackbar as opened */\n  _open() {\n    if (!this._afterOpened.closed) {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    }\n  }\n  /** Cleans up the DOM after closing. */\n  _finishDismiss() {\n    this._overlayRef.dispose();\n    if (!this._onAction.closed) {\n      this._onAction.complete();\n    }\n    this._afterDismissed.next({\n      dismissedByAction: this._dismissedByAction\n    });\n    this._afterDismissed.complete();\n    this._dismissedByAction = false;\n  }\n  /** Gets an observable that is notified when the snack bar is finished closing. */\n  afterDismissed() {\n    return this._afterDismissed;\n  }\n  /** Gets an observable that is notified when the snack bar has opened and appeared. */\n  afterOpened() {\n    return this.containerInstance._onEnter;\n  }\n  /** Gets an observable that is notified when the snack bar action is called. */\n  onAction() {\n    return this._onAction;\n  }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n  constructor() {\n    /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n    this.politeness = 'assertive';\n    /**\n     * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n     * component or template, the announcement message will default to the specified message.\n     */\n    this.announcementMessage = '';\n    /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n    this.duration = 0;\n    /** Data being injected into the child component. */\n    this.data = null;\n    /** The horizontal position to place the snack bar. */\n    this.horizontalPosition = 'center';\n    /** The vertical position to place the snack bar. */\n    this.verticalPosition = 'bottom';\n  }\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {\n  static {\n    this.ɵfac = function MatSnackBarLabel_Factory(t) {\n      return new (t || MatSnackBarLabel)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSnackBarLabel,\n      selectors: [[\"\", \"matSnackBarLabel\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-snack-bar-label\", \"mdc-snackbar__label\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarLabel, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarLabel]`,\n      standalone: true,\n      host: {\n        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {\n  static {\n    this.ɵfac = function MatSnackBarActions_Factory(t) {\n      return new (t || MatSnackBarActions)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSnackBarActions,\n      selectors: [[\"\", \"matSnackBarActions\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-snack-bar-actions\", \"mdc-snackbar__actions\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarActions, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarActions]`,\n      standalone: true,\n      host: {\n        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {\n  static {\n    this.ɵfac = function MatSnackBarAction_Factory(t) {\n      return new (t || MatSnackBarAction)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSnackBarAction,\n      selectors: [[\"\", \"matSnackBarAction\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-snack-bar-action\", \"mdc-snackbar__action\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarAction, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarAction]`,\n      standalone: true,\n      host: {\n        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action'\n      }\n    }]\n  }], null, null);\n})();\nclass SimpleSnackBar {\n  constructor(snackBarRef, data) {\n    this.snackBarRef = snackBarRef;\n    this.data = data;\n  }\n  /** Performs the action on the snack bar. */\n  action() {\n    this.snackBarRef.dismissWithAction();\n  }\n  /** If the action button should be shown. */\n  get hasAction() {\n    return !!this.data.action;\n  }\n  static {\n    this.ɵfac = function SimpleSnackBar_Factory(t) {\n      return new (t || SimpleSnackBar)(i0.ɵɵdirectiveInject(MatSnackBarRef), i0.ɵɵdirectiveInject(MAT_SNACK_BAR_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SimpleSnackBar,\n      selectors: [[\"simple-snack-bar\"]],\n      hostAttrs: [1, \"mat-mdc-simple-snack-bar\"],\n      exportAs: [\"matSnackBar\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[\"matSnackBarLabel\", \"\"], [\"matSnackBarActions\", \"\"], [\"mat-button\", \"\", \"matSnackBarAction\", \"\", 3, \"click\"]],\n      template: function SimpleSnackBar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, SimpleSnackBar_Conditional_2_Template, 3, 1, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.data.message, \"\\n\");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(2, ctx.hasAction ? 2 : -1);\n        }\n      },\n      dependencies: [MatButtonModule, i2.MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction, CommonModule],\n      styles: [\".mat-mdc-simple-snack-bar{display:flex}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SimpleSnackBar, [{\n    type: Component,\n    args: [{\n      selector: 'simple-snack-bar',\n      exportAs: 'matSnackBar',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatButtonModule, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction, CommonModule],\n      standalone: true,\n      host: {\n        'class': 'mat-mdc-simple-snack-bar'\n      },\n      template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\",\n      styles: [\".mat-mdc-simple-snack-bar{display:flex}\"]\n    }]\n  }], () => [{\n    type: MatSnackBarRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_SNACK_BAR_DATA]\n    }]\n  }], null);\n})();\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n */\nconst matSnackBarAnimations = {\n  /** Animation that shows and hides a snack bar. */\n  snackBarState: trigger('state', [state('void, hidden', style({\n    transform: 'scale(0.8)',\n    opacity: 0\n  })), state('visible', style({\n    transform: 'scale(1)',\n    opacity: 1\n  })), transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')), transition('* => void, * => hidden', animate('75ms cubic-bezier(0.4, 0.0, 1, 1)', style({\n    opacity: 0\n  })))])\n};\nlet uniqueId = 0;\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends BasePortalOutlet {\n  constructor(_ngZone, _elementRef, _changeDetectorRef, _platform, /** The snack bar configuration. */\n  snackBarConfig) {\n    super();\n    this._ngZone = _ngZone;\n    this._elementRef = _elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._platform = _platform;\n    this.snackBarConfig = snackBarConfig;\n    this._document = inject(DOCUMENT);\n    this._trackedModals = new Set();\n    /** The number of milliseconds to wait before announcing the snack bar's content. */\n    this._announceDelay = 150;\n    /** Whether the component has been destroyed. */\n    this._destroyed = false;\n    /** Subject for notifying that the snack bar has announced to screen readers. */\n    this._onAnnounce = new Subject();\n    /** Subject for notifying that the snack bar has exited from view. */\n    this._onExit = new Subject();\n    /** Subject for notifying that the snack bar has finished entering the view. */\n    this._onEnter = new Subject();\n    /** The state of the snack bar animations. */\n    this._animationState = 'void';\n    /** Unique ID of the aria-live element. */\n    this._liveElementId = `mat-snack-bar-container-live-${uniqueId++}`;\n    /**\n     * Attaches a DOM portal to the snack bar container.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    this.attachDomPortal = portal => {\n      this._assertNotAttached();\n      const result = this._portalOutlet.attachDomPortal(portal);\n      this._afterPortalAttached();\n      return result;\n    };\n    // Use aria-live rather than a live role like 'alert' or 'status'\n    // because NVDA and JAWS have show inconsistent behavior with live roles.\n    if (snackBarConfig.politeness === 'assertive' && !snackBarConfig.announcementMessage) {\n      this._live = 'assertive';\n    } else if (snackBarConfig.politeness === 'off') {\n      this._live = 'off';\n    } else {\n      this._live = 'polite';\n    }\n    // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n    // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n    if (this._platform.FIREFOX) {\n      if (this._live === 'polite') {\n        this._role = 'status';\n      }\n      if (this._live === 'assertive') {\n        this._role = 'alert';\n      }\n    }\n  }\n  /** Attach a component portal as content to this snack bar container. */\n  attachComponentPortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /** Attach a template portal as content to this snack bar container. */\n  attachTemplatePortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /** Handle end of animations, updating the state of the snackbar. */\n  onAnimationEnd(event) {\n    const {\n      fromState,\n      toState\n    } = event;\n    if (toState === 'void' && fromState !== 'void' || toState === 'hidden') {\n      this._completeExit();\n    }\n    if (toState === 'visible') {\n      // Note: we shouldn't use `this` inside the zone callback,\n      // because it can cause a memory leak.\n      const onEnter = this._onEnter;\n      this._ngZone.run(() => {\n        onEnter.next();\n        onEnter.complete();\n      });\n    }\n  }\n  /** Begin animation of snack bar entrance into view. */\n  enter() {\n    if (!this._destroyed) {\n      this._animationState = 'visible';\n      this._changeDetectorRef.detectChanges();\n      this._screenReaderAnnounce();\n    }\n  }\n  /** Begin animation of the snack bar exiting from view. */\n  exit() {\n    // It's common for snack bars to be opened by random outside calls like HTTP requests or\n    // errors. Run inside the NgZone to ensure that it functions correctly.\n    this._ngZone.run(() => {\n      // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n      // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n      // `MatSnackBar.open`).\n      this._animationState = 'hidden';\n      // Mark this element with an 'exit' attribute to indicate that the snackbar has\n      // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n      // test harness.\n      this._elementRef.nativeElement.setAttribute('mat-exit', '');\n      // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n      // long enough to visually read it either, so clear the timeout for announcing.\n      clearTimeout(this._announceTimeoutId);\n    });\n    return this._onExit;\n  }\n  /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n  ngOnDestroy() {\n    this._destroyed = true;\n    this._clearFromModals();\n    this._completeExit();\n  }\n  /**\n   * Waits for the zone to settle before removing the element. Helps prevent\n   * errors where we end up removing an element which is in the middle of an animation.\n   */\n  _completeExit() {\n    this._ngZone.onMicrotaskEmpty.pipe(take(1)).subscribe(() => {\n      this._ngZone.run(() => {\n        this._onExit.next();\n        this._onExit.complete();\n      });\n    });\n  }\n  /**\n   * Called after the portal contents have been attached. Can be\n   * used to modify the DOM once it's guaranteed to be in place.\n   */\n  _afterPortalAttached() {\n    const element = this._elementRef.nativeElement;\n    const panelClasses = this.snackBarConfig.panelClass;\n    if (panelClasses) {\n      if (Array.isArray(panelClasses)) {\n        // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n        panelClasses.forEach(cssClass => element.classList.add(cssClass));\n      } else {\n        element.classList.add(panelClasses);\n      }\n    }\n    this._exposeToModals();\n    // Check to see if the attached component or template uses the MDC template structure,\n    // specifically the MDC label. If not, the container should apply the MDC label class to this\n    // component's label container, which will apply MDC's label styles to the attached view.\n    const label = this._label.nativeElement;\n    const labelClass = 'mdc-snackbar__label';\n    label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live element if there is an\n   * `aria-modal` and the live element is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live element.\n   */\n  _exposeToModals() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n    // `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const id = this._liveElementId;\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      this._trackedModals.add(modal);\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  /** Clears the references to the live element from any modals it was added to. */\n  _clearFromModals() {\n    this._trackedModals.forEach(modal => {\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (ariaOwns) {\n        const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n        if (newValue.length > 0) {\n          modal.setAttribute('aria-owns', newValue);\n        } else {\n          modal.removeAttribute('aria-owns');\n        }\n      }\n    });\n    this._trackedModals.clear();\n  }\n  /** Asserts that no content is already attached to the container. */\n  _assertNotAttached() {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Attempting to attach snack bar content after content is already attached');\n    }\n  }\n  /**\n   * Starts a timeout to move the snack bar content to the live region so screen readers will\n   * announce it.\n   */\n  _screenReaderAnnounce() {\n    if (!this._announceTimeoutId) {\n      this._ngZone.runOutsideAngular(() => {\n        this._announceTimeoutId = setTimeout(() => {\n          const inertElement = this._elementRef.nativeElement.querySelector('[aria-hidden]');\n          const liveElement = this._elementRef.nativeElement.querySelector('[aria-live]');\n          if (inertElement && liveElement) {\n            // If an element in the snack bar content is focused before being moved\n            // track it and restore focus after moving to the live region.\n            let focusedElement = null;\n            if (this._platform.isBrowser && document.activeElement instanceof HTMLElement && inertElement.contains(document.activeElement)) {\n              focusedElement = document.activeElement;\n            }\n            inertElement.removeAttribute('aria-hidden');\n            liveElement.appendChild(inertElement);\n            focusedElement?.focus();\n            this._onAnnounce.next();\n            this._onAnnounce.complete();\n          }\n        }, this._announceDelay);\n      });\n    }\n  }\n  static {\n    this.ɵfac = function MatSnackBarContainer_Factory(t) {\n      return new (t || MatSnackBarContainer)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(MatSnackBarConfig));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSnackBarContainer,\n      selectors: [[\"mat-snack-bar-container\"]],\n      viewQuery: function MatSnackBarContainer_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._label = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mdc-snackbar\", \"mat-mdc-snack-bar-container\", \"mdc-snackbar--open\"],\n      hostVars: 1,\n      hostBindings: function MatSnackBarContainer_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵsyntheticHostListener(\"@state.done\", function MatSnackBarContainer_animation_state_done_HostBindingHandler($event) {\n            return ctx.onAnimationEnd($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@state\", ctx._animationState);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 3,\n      consts: [[\"label\", \"\"], [1, \"mdc-snackbar__surface\"], [1, \"mat-mdc-snack-bar-label\"], [\"aria-hidden\", \"true\"], [\"cdkPortalOutlet\", \"\"]],\n      template: function MatSnackBarContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2, 0)(3, \"div\", 3);\n          i0.ɵɵtemplate(4, MatSnackBarContainer_ng_template_4_Template, 0, 0, \"ng-template\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"div\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵattribute(\"aria-live\", ctx._live)(\"role\", ctx._role)(\"id\", ctx._liveElementId);\n        }\n      },\n      dependencies: [PortalModule, i3.CdkPortalOutlet],\n      styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape)}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size);font-family:var(--mdc-snackbar-supporting-text-font);font-weight:var(--mdc-snackbar-supporting-text-weight);line-height:var(--mdc-snackbar-supporting-text-line-height)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-snack-bar-button-color);--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matSnackBarAnimations.snackBarState]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-snack-bar-container',\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      animations: [matSnackBarAnimations.snackBarState],\n      standalone: true,\n      imports: [PortalModule],\n      host: {\n        'class': 'mdc-snackbar mat-mdc-snack-bar-container mdc-snackbar--open',\n        '[@state]': '_animationState',\n        '(@state.done)': 'onAnimationEnd($event)'\n      },\n      template: \"<div class=\\\"mdc-snackbar__surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet></ng-template>\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape)}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size);font-family:var(--mdc-snackbar-supporting-text-font);font-weight:var(--mdc-snackbar-supporting-text-weight);line-height:var(--mdc-snackbar-supporting-text-line-height)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-snack-bar-button-color);--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"]\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.Platform\n  }, {\n    type: MatSnackBarConfig\n  }], {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }],\n    _label: [{\n      type: ViewChild,\n      args: ['label', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/** @docs-private */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n  return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n  providedIn: 'root',\n  factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar {\n  /** Reference to the currently opened snackbar at *any* level. */\n  get _openedSnackBarRef() {\n    const parent = this._parentSnackBar;\n    return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n  }\n  set _openedSnackBarRef(value) {\n    if (this._parentSnackBar) {\n      this._parentSnackBar._openedSnackBarRef = value;\n    } else {\n      this._snackBarRefAtThisLevel = value;\n    }\n  }\n  constructor(_overlay, _live, _injector, _breakpointObserver, _parentSnackBar, _defaultConfig) {\n    this._overlay = _overlay;\n    this._live = _live;\n    this._injector = _injector;\n    this._breakpointObserver = _breakpointObserver;\n    this._parentSnackBar = _parentSnackBar;\n    this._defaultConfig = _defaultConfig;\n    /**\n     * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n     * If there is a parent snack-bar service, all operations should delegate to that parent\n     * via `_openedSnackBarRef`.\n     */\n    this._snackBarRefAtThisLevel = null;\n    /** The component that should be rendered as the snack bar's simple component. */\n    this.simpleSnackBarComponent = SimpleSnackBar;\n    /** The container component that attaches the provided template or component. */\n    this.snackBarContainerComponent = MatSnackBarContainer;\n    /** The CSS class to apply for handset mode. */\n    this.handsetCssClass = 'mat-mdc-snack-bar-handset';\n  }\n  /**\n   * Creates and dispatches a snack bar with a custom component for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param component Component to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromComponent(component, config) {\n    return this._attach(component, config);\n  }\n  /**\n   * Creates and dispatches a snack bar with a custom template for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param template Template to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromTemplate(template, config) {\n    return this._attach(template, config);\n  }\n  /**\n   * Opens a snackbar with a message and an optional action.\n   * @param message The message to show in the snackbar.\n   * @param action The label for the snackbar action.\n   * @param config Additional configuration options for the snackbar.\n   */\n  open(message, action = '', config) {\n    const _config = {\n      ...this._defaultConfig,\n      ...config\n    };\n    // Since the user doesn't have access to the component, we can\n    // override the data to pass in our own message and action.\n    _config.data = {\n      message,\n      action\n    };\n    // Since the snack bar has `role=\"alert\"`, we don't\n    // want to announce the same message twice.\n    if (_config.announcementMessage === message) {\n      _config.announcementMessage = undefined;\n    }\n    return this.openFromComponent(this.simpleSnackBarComponent, _config);\n  }\n  /**\n   * Dismisses the currently-visible snack bar.\n   */\n  dismiss() {\n    if (this._openedSnackBarRef) {\n      this._openedSnackBarRef.dismiss();\n    }\n  }\n  ngOnDestroy() {\n    // Only dismiss the snack bar at the current level on destroy.\n    if (this._snackBarRefAtThisLevel) {\n      this._snackBarRefAtThisLevel.dismiss();\n    }\n  }\n  /**\n   * Attaches the snack bar container component to the overlay.\n   */\n  _attachSnackBarContainer(overlayRef, config) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    const injector = Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarConfig,\n        useValue: config\n      }]\n    });\n    const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n    const containerRef = overlayRef.attach(containerPortal);\n    containerRef.instance.snackBarConfig = config;\n    return containerRef.instance;\n  }\n  /**\n   * Places a new component or a template as the content of the snack bar container.\n   */\n  _attach(content, userConfig) {\n    const config = {\n      ...new MatSnackBarConfig(),\n      ...this._defaultConfig,\n      ...userConfig\n    };\n    const overlayRef = this._createOverlay(config);\n    const container = this._attachSnackBarContainer(overlayRef, config);\n    const snackBarRef = new MatSnackBarRef(container, overlayRef);\n    if (content instanceof TemplateRef) {\n      const portal = new TemplatePortal(content, null, {\n        $implicit: config.data,\n        snackBarRef\n      });\n      snackBarRef.instance = container.attachTemplatePortal(portal);\n    } else {\n      const injector = this._createInjector(config, snackBarRef);\n      const portal = new ComponentPortal(content, undefined, injector);\n      const contentRef = container.attachComponentPortal(portal);\n      // We can't pass this via the injector, because the injector is created earlier.\n      snackBarRef.instance = contentRef.instance;\n    }\n    // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n    // appropriate. This class is applied to the overlay element because the overlay must expand to\n    // fill the width of the screen for full width snackbars.\n    this._breakpointObserver.observe(Breakpoints.HandsetPortrait).pipe(takeUntil(overlayRef.detachments())).subscribe(state => {\n      overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n    });\n    if (config.announcementMessage) {\n      // Wait until the snack bar contents have been announced then deliver this message.\n      container._onAnnounce.subscribe(() => {\n        this._live.announce(config.announcementMessage, config.politeness);\n      });\n    }\n    this._animateSnackBar(snackBarRef, config);\n    this._openedSnackBarRef = snackBarRef;\n    return this._openedSnackBarRef;\n  }\n  /** Animates the old snack bar out and the new one in. */\n  _animateSnackBar(snackBarRef, config) {\n    // When the snackbar is dismissed, clear the reference to it.\n    snackBarRef.afterDismissed().subscribe(() => {\n      // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n      if (this._openedSnackBarRef == snackBarRef) {\n        this._openedSnackBarRef = null;\n      }\n      if (config.announcementMessage) {\n        this._live.clear();\n      }\n    });\n    if (this._openedSnackBarRef) {\n      // If a snack bar is already in view, dismiss it and enter the\n      // new snack bar after exit animation is complete.\n      this._openedSnackBarRef.afterDismissed().subscribe(() => {\n        snackBarRef.containerInstance.enter();\n      });\n      this._openedSnackBarRef.dismiss();\n    } else {\n      // If no snack bar is in view, enter the new snack bar.\n      snackBarRef.containerInstance.enter();\n    }\n    // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n    if (config.duration && config.duration > 0) {\n      snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n    }\n  }\n  /**\n   * Creates a new overlay and places it in the correct location.\n   * @param config The user-specified snack bar config.\n   */\n  _createOverlay(config) {\n    const overlayConfig = new OverlayConfig();\n    overlayConfig.direction = config.direction;\n    let positionStrategy = this._overlay.position().global();\n    // Set horizontal position.\n    const isRtl = config.direction === 'rtl';\n    const isLeft = config.horizontalPosition === 'left' || config.horizontalPosition === 'start' && !isRtl || config.horizontalPosition === 'end' && isRtl;\n    const isRight = !isLeft && config.horizontalPosition !== 'center';\n    if (isLeft) {\n      positionStrategy.left('0');\n    } else if (isRight) {\n      positionStrategy.right('0');\n    } else {\n      positionStrategy.centerHorizontally();\n    }\n    // Set horizontal position.\n    if (config.verticalPosition === 'top') {\n      positionStrategy.top('0');\n    } else {\n      positionStrategy.bottom('0');\n    }\n    overlayConfig.positionStrategy = positionStrategy;\n    return this._overlay.create(overlayConfig);\n  }\n  /**\n   * Creates an injector to be used inside of a snack bar component.\n   * @param config Config that was used to create the snack bar.\n   * @param snackBarRef Reference to the snack bar.\n   */\n  _createInjector(config, snackBarRef) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    return Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarRef,\n        useValue: snackBarRef\n      }, {\n        provide: MAT_SNACK_BAR_DATA,\n        useValue: config.data\n      }]\n    });\n  }\n  static {\n    this.ɵfac = function MatSnackBar_Factory(t) {\n      return new (t || MatSnackBar)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i2$1.LiveAnnouncer), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i3$1.BreakpointObserver), i0.ɵɵinject(MatSnackBar, 12), i0.ɵɵinject(MAT_SNACK_BAR_DEFAULT_OPTIONS));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatSnackBar,\n      factory: MatSnackBar.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBar, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1$1.Overlay\n  }, {\n    type: i2$1.LiveAnnouncer\n  }, {\n    type: i0.Injector\n  }, {\n    type: i3$1.BreakpointObserver\n  }, {\n    type: MatSnackBar,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: MatSnackBarConfig,\n    decorators: [{\n      type: Inject,\n      args: [MAT_SNACK_BAR_DEFAULT_OPTIONS]\n    }]\n  }], null);\n})();\nconst DIRECTIVES = [MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction];\nclass MatSnackBarModule {\n  static {\n    this.ɵfac = function MatSnackBarModule_Factory(t) {\n      return new (t || MatSnackBarModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSnackBarModule,\n      imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n      exports: [MatCommonModule, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MatSnackBar],\n      imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, MatSnackBarContainer, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, ...DIRECTIVES],\n      exports: [MatCommonModule, ...DIRECTIVES],\n      providers: [MatSnackBar]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, matSnackBarAnimations };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,UAAU,CAAC;AAC7C,IAAG,WAAW,SAAS,SAAS,gEAAgE;AAC9F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAK,OAAO,KAAK,QAAQ,GAAG;AAAA,EACpD;AACF;AACA,IAAM,MAAM,CAAC,OAAO;AACpB,SAAS,4CAA4C,IAAI,KAAK;AAAC;AAC/D,IAAM,cAAc,KAAK,IAAI,GAAG,EAAE,IAAI;AAItC,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,mBAAmB,aAAa;AAC1C,SAAK,cAAc;AAEnB,SAAK,kBAAkB,IAAI,QAAQ;AAEnC,SAAK,eAAe,IAAI,QAAQ;AAEhC,SAAK,YAAY,IAAI,QAAQ;AAE7B,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB;AACzB,sBAAkB,QAAQ,UAAU,MAAM,KAAK,eAAe,CAAC;AAAA,EACjE;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,CAAC,KAAK,gBAAgB,QAAQ;AAChC,WAAK,kBAAkB,KAAK;AAAA,IAC9B;AACA,iBAAa,KAAK,kBAAkB;AAAA,EACtC;AAAA;AAAA,EAEA,oBAAoB;AAClB,QAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,WAAK,qBAAqB;AAC1B,WAAK,UAAU,KAAK;AACpB,WAAK,UAAU,SAAS;AACxB,WAAK,QAAQ;AAAA,IACf;AACA,iBAAa,KAAK,kBAAkB;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA,EAEA,cAAc,UAAU;AAGtB,SAAK,qBAAqB,WAAW,MAAM,KAAK,QAAQ,GAAG,KAAK,IAAI,UAAU,WAAW,CAAC;AAAA,EAC5F;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,CAAC,KAAK,aAAa,QAAQ;AAC7B,WAAK,aAAa,KAAK;AACvB,WAAK,aAAa,SAAS;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,YAAY,QAAQ;AACzB,QAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,WAAK,UAAU,SAAS;AAAA,IAC1B;AACA,SAAK,gBAAgB,KAAK;AAAA,MACxB,mBAAmB,KAAK;AAAA,IAC1B,CAAC;AACD,SAAK,gBAAgB,SAAS;AAC9B,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,kBAAkB;AAAA,EAChC;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AACF;AAGA,IAAM,qBAAqB,IAAI,eAAe,iBAAiB;AAI/D,IAAM,oBAAN,MAAwB;AAAA,EACtB,cAAc;AAEZ,SAAK,aAAa;AAKlB,SAAK,sBAAsB;AAE3B,SAAK,WAAW;AAEhB,SAAK,OAAO;AAEZ,SAAK,qBAAqB;AAE1B,SAAK,mBAAmB;AAAA,EAC1B;AACF;AAGA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,MACxC,WAAW,CAAC,GAAG,2BAA2B,qBAAqB;AAAA,MAC/D,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,MAC1C,WAAW,CAAC,GAAG,6BAA6B,uBAAuB;AAAA,MACnE,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAmB;AAAA,IACtC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,MACzC,WAAW,CAAC,GAAG,4BAA4B,sBAAsB;AAAA,MACjE,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,aAAa,MAAM;AAC7B,SAAK,cAAc;AACnB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,YAAY,kBAAkB;AAAA,EACrC;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,CAAC,CAAC,KAAK,KAAK;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAmB,kBAAkB,cAAc,GAAM,kBAAkB,kBAAkB,CAAC;AAAA,IACjH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,WAAW,CAAC,GAAG,0BAA0B;AAAA,MACzC,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,oBAAoB,EAAE,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,cAAc,IAAI,qBAAqB,IAAI,GAAG,OAAO,CAAC;AAAA,MACtH,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,OAAO,CAAC;AACX,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,uCAAuC,GAAG,GAAG,OAAO,CAAC;AAAA,QACxE;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,mBAAmB,KAAK,IAAI,KAAK,SAAS,IAAI;AACjD,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,YAAY,IAAI,EAAE;AAAA,QAC5C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,iBAAoB,WAAW,kBAAkB,oBAAoB,mBAAmB,YAAY;AAAA,MACnH,QAAQ,CAAC,yCAAyC;AAAA,MAClD,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,iBAAiB,kBAAkB,oBAAoB,mBAAmB,YAAY;AAAA,MAChG,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,UAAU;AAAA,MACV,QAAQ,CAAC,yCAAyC;AAAA,IACpD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,wBAAwB;AAAA;AAAA,EAE5B,eAAe,QAAQ,SAAS,CAAC,MAAM,gBAAgB,MAAM;AAAA,IAC3D,WAAW;AAAA,IACX,SAAS;AAAA,EACX,CAAC,CAAC,GAAG,MAAM,WAAW,MAAM;AAAA,IAC1B,WAAW;AAAA,IACX,SAAS;AAAA,EACX,CAAC,CAAC,GAAG,WAAW,gBAAgB,QAAQ,kCAAkC,CAAC,GAAG,WAAW,0BAA0B,QAAQ,qCAAqC,MAAM;AAAA,IACpK,SAAS;AAAA,EACX,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AACA,IAAI,WAAW;AAKf,IAAM,uBAAN,MAAM,8BAA6B,iBAAiB;AAAA,EAClD,YAAY,SAAS,aAAa,oBAAoB,WACtD,gBAAgB;AACd,UAAM;AACN,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,YAAY,OAAO,QAAQ;AAChC,SAAK,iBAAiB,oBAAI,IAAI;AAE9B,SAAK,iBAAiB;AAEtB,SAAK,aAAa;AAElB,SAAK,cAAc,IAAI,QAAQ;AAE/B,SAAK,UAAU,IAAI,QAAQ;AAE3B,SAAK,WAAW,IAAI,QAAQ;AAE5B,SAAK,kBAAkB;AAEvB,SAAK,iBAAiB,gCAAgC,UAAU;AAMhE,SAAK,kBAAkB,YAAU;AAC/B,WAAK,mBAAmB;AACxB,YAAM,SAAS,KAAK,cAAc,gBAAgB,MAAM;AACxD,WAAK,qBAAqB;AAC1B,aAAO;AAAA,IACT;AAGA,QAAI,eAAe,eAAe,eAAe,CAAC,eAAe,qBAAqB;AACpF,WAAK,QAAQ;AAAA,IACf,WAAW,eAAe,eAAe,OAAO;AAC9C,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AAGA,QAAI,KAAK,UAAU,SAAS;AAC1B,UAAI,KAAK,UAAU,UAAU;AAC3B,aAAK,QAAQ;AAAA,MACf;AACA,UAAI,KAAK,UAAU,aAAa;AAC9B,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,sBAAsB,QAAQ;AAC5B,SAAK,mBAAmB;AACxB,UAAM,SAAS,KAAK,cAAc,sBAAsB,MAAM;AAC9D,SAAK,qBAAqB;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,qBAAqB,QAAQ;AAC3B,SAAK,mBAAmB;AACxB,UAAM,SAAS,KAAK,cAAc,qBAAqB,MAAM;AAC7D,SAAK,qBAAqB;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,YAAY,UAAU,cAAc,UAAU,YAAY,UAAU;AACtE,WAAK,cAAc;AAAA,IACrB;AACA,QAAI,YAAY,WAAW;AAGzB,YAAM,UAAU,KAAK;AACrB,WAAK,QAAQ,IAAI,MAAM;AACrB,gBAAQ,KAAK;AACb,gBAAQ,SAAS;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,kBAAkB;AACvB,WAAK,mBAAmB,cAAc;AACtC,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA,EAEA,OAAO;AAGL,SAAK,QAAQ,IAAI,MAAM;AAIrB,WAAK,kBAAkB;AAIvB,WAAK,YAAY,cAAc,aAAa,YAAY,EAAE;AAG1D,mBAAa,KAAK,kBAAkB;AAAA,IACtC,CAAC;AACD,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,SAAK,QAAQ,iBAAiB,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AAC1D,WAAK,QAAQ,IAAI,MAAM;AACrB,aAAK,QAAQ,KAAK;AAClB,aAAK,QAAQ,SAAS;AAAA,MACxB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,eAAe,KAAK,eAAe;AACzC,QAAI,cAAc;AAChB,UAAI,MAAM,QAAQ,YAAY,GAAG;AAE/B,qBAAa,QAAQ,cAAY,QAAQ,UAAU,IAAI,QAAQ,CAAC;AAAA,MAClE,OAAO;AACL,gBAAQ,UAAU,IAAI,YAAY;AAAA,MACpC;AAAA,IACF;AACA,SAAK,gBAAgB;AAIrB,UAAM,QAAQ,KAAK,OAAO;AAC1B,UAAM,aAAa;AACnB,UAAM,UAAU,OAAO,YAAY,CAAC,MAAM,cAAc,IAAI,UAAU,EAAE,CAAC;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAOhB,UAAM,KAAK,KAAK;AAChB,UAAM,SAAS,KAAK,UAAU,iBAAiB,mDAAmD;AAClG,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,QAAQ,OAAO,CAAC;AACtB,YAAM,WAAW,MAAM,aAAa,WAAW;AAC/C,WAAK,eAAe,IAAI,KAAK;AAC7B,UAAI,CAAC,UAAU;AACb,cAAM,aAAa,aAAa,EAAE;AAAA,MACpC,WAAW,SAAS,QAAQ,EAAE,MAAM,IAAI;AACtC,cAAM,aAAa,aAAa,WAAW,MAAM,EAAE;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,eAAe,QAAQ,WAAS;AACnC,YAAM,WAAW,MAAM,aAAa,WAAW;AAC/C,UAAI,UAAU;AACZ,cAAM,WAAW,SAAS,QAAQ,KAAK,gBAAgB,EAAE,EAAE,KAAK;AAChE,YAAI,SAAS,SAAS,GAAG;AACvB,gBAAM,aAAa,aAAa,QAAQ;AAAA,QAC1C,OAAO;AACL,gBAAM,gBAAgB,WAAW;AAAA,QACnC;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,eAAe,MAAM;AAAA,EAC5B;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,KAAK,cAAc,YAAY,MAAM,OAAO,cAAc,eAAe,YAAY;AACvF,YAAM,MAAM,0EAA0E;AAAA,IACxF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,QAAI,CAAC,KAAK,oBAAoB;AAC5B,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,qBAAqB,WAAW,MAAM;AACzC,gBAAM,eAAe,KAAK,YAAY,cAAc,cAAc,eAAe;AACjF,gBAAM,cAAc,KAAK,YAAY,cAAc,cAAc,aAAa;AAC9E,cAAI,gBAAgB,aAAa;AAG/B,gBAAI,iBAAiB;AACrB,gBAAI,KAAK,UAAU,aAAa,SAAS,yBAAyB,eAAe,aAAa,SAAS,SAAS,aAAa,GAAG;AAC9H,+BAAiB,SAAS;AAAA,YAC5B;AACA,yBAAa,gBAAgB,aAAa;AAC1C,wBAAY,YAAY,YAAY;AACpC,4BAAgB,MAAM;AACtB,iBAAK,YAAY,KAAK;AACtB,iBAAK,YAAY,SAAS;AAAA,UAC5B;AAAA,QACF,GAAG,KAAK,cAAc;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,kBAAqB,MAAM,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,QAAQ,GAAM,kBAAkB,iBAAiB,CAAC;AAAA,IACrO;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,yBAAyB,CAAC;AAAA,MACvC,WAAW,SAAS,2BAA2B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,iBAAiB,CAAC;AACjC,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS,GAAG;AAAA,QAC/D;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,gBAAgB,+BAA+B,oBAAoB;AAAA,MAClF,UAAU;AAAA,MACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,eAAe,SAAS,6DAA6D,QAAQ;AACtH,mBAAO,IAAI,eAAe,MAAM;AAAA,UAClC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,UAAU,IAAI,eAAe;AAAA,QAC1D;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,4BAA+B,mBAAmB;AAAA,MAChE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,eAAe,MAAM,GAAG,CAAC,mBAAmB,EAAE,CAAC;AAAA,MACtI,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1D,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,eAAe,CAAC;AACpF,UAAG,aAAa;AAChB,UAAG,UAAU,GAAG,KAAK;AACrB,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,aAAa,IAAI,KAAK,EAAE,QAAQ,IAAI,KAAK,EAAE,MAAM,IAAI,cAAc;AAAA,QACpF;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAiB,eAAe;AAAA,MAC/C,QAAQ,CAAC,u4GAAy4G;AAAA,MACl5G,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,sBAAsB,aAAa;AAAA,MACjD;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,YAAY,CAAC,sBAAsB,aAAa;AAAA,MAChD,YAAY;AAAA,MACZ,SAAS,CAAC,YAAY;AAAA,MACtB,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU;AAAA,MACV,QAAQ,CAAC,u4GAAy4G;AAAA,IACp5G,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,QACd,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,SAAS,wCAAwC;AAC/C,SAAO,IAAI,kBAAkB;AAC/B;AAEA,IAAM,gCAAgC,IAAI,eAAe,iCAAiC;AAAA,EACxF,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAID,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA,EAEhB,IAAI,qBAAqB;AACvB,UAAM,SAAS,KAAK;AACpB,WAAO,SAAS,OAAO,qBAAqB,KAAK;AAAA,EACnD;AAAA,EACA,IAAI,mBAAmB,OAAO;AAC5B,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,qBAAqB;AAAA,IAC5C,OAAO;AACL,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AAAA,EACA,YAAY,UAAU,OAAO,WAAW,qBAAqB,iBAAiB,gBAAgB;AAC5F,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AAMtB,SAAK,0BAA0B;AAE/B,SAAK,0BAA0B;AAE/B,SAAK,6BAA6B;AAElC,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB,WAAW,QAAQ;AACnC,WAAO,KAAK,QAAQ,WAAW,MAAM;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,UAAU,QAAQ;AACjC,WAAO,KAAK,QAAQ,UAAU,MAAM;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,SAAS,SAAS,IAAI,QAAQ;AACjC,UAAM,UAAU,kCACX,KAAK,iBACL;AAIL,YAAQ,OAAO;AAAA,MACb;AAAA,MACA;AAAA,IACF;AAGA,QAAI,QAAQ,wBAAwB,SAAS;AAC3C,cAAQ,sBAAsB;AAAA,IAChC;AACA,WAAO,KAAK,kBAAkB,KAAK,yBAAyB,OAAO;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,QAAI,KAAK,oBAAoB;AAC3B,WAAK,mBAAmB,QAAQ;AAAA,IAClC;AAAA,EACF;AAAA,EACA,cAAc;AAEZ,QAAI,KAAK,yBAAyB;AAChC,WAAK,wBAAwB,QAAQ;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,yBAAyB,YAAY,QAAQ;AAC3C,UAAM,eAAe,UAAU,OAAO,oBAAoB,OAAO,iBAAiB;AAClF,UAAM,WAAW,SAAS,OAAO;AAAA,MAC/B,QAAQ,gBAAgB,KAAK;AAAA,MAC7B,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AACD,UAAM,kBAAkB,IAAI,gBAAgB,KAAK,4BAA4B,OAAO,kBAAkB,QAAQ;AAC9G,UAAM,eAAe,WAAW,OAAO,eAAe;AACtD,iBAAa,SAAS,iBAAiB;AACvC,WAAO,aAAa;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,SAAS,YAAY;AAC3B,UAAM,SAAS,iDACV,IAAI,kBAAkB,IACtB,KAAK,iBACL;AAEL,UAAM,aAAa,KAAK,eAAe,MAAM;AAC7C,UAAM,YAAY,KAAK,yBAAyB,YAAY,MAAM;AAClE,UAAM,cAAc,IAAI,eAAe,WAAW,UAAU;AAC5D,QAAI,mBAAmB,aAAa;AAClC,YAAM,SAAS,IAAI,eAAe,SAAS,MAAM;AAAA,QAC/C,WAAW,OAAO;AAAA,QAClB;AAAA,MACF,CAAC;AACD,kBAAY,WAAW,UAAU,qBAAqB,MAAM;AAAA,IAC9D,OAAO;AACL,YAAM,WAAW,KAAK,gBAAgB,QAAQ,WAAW;AACzD,YAAM,SAAS,IAAI,gBAAgB,SAAS,QAAW,QAAQ;AAC/D,YAAM,aAAa,UAAU,sBAAsB,MAAM;AAEzD,kBAAY,WAAW,WAAW;AAAA,IACpC;AAIA,SAAK,oBAAoB,QAAQ,YAAY,eAAe,EAAE,KAAK,UAAU,WAAW,YAAY,CAAC,CAAC,EAAE,UAAU,CAAAA,WAAS;AACzH,iBAAW,eAAe,UAAU,OAAO,KAAK,iBAAiBA,OAAM,OAAO;AAAA,IAChF,CAAC;AACD,QAAI,OAAO,qBAAqB;AAE9B,gBAAU,YAAY,UAAU,MAAM;AACpC,aAAK,MAAM,SAAS,OAAO,qBAAqB,OAAO,UAAU;AAAA,MACnE,CAAC;AAAA,IACH;AACA,SAAK,iBAAiB,aAAa,MAAM;AACzC,SAAK,qBAAqB;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB,aAAa,QAAQ;AAEpC,gBAAY,eAAe,EAAE,UAAU,MAAM;AAE3C,UAAI,KAAK,sBAAsB,aAAa;AAC1C,aAAK,qBAAqB;AAAA,MAC5B;AACA,UAAI,OAAO,qBAAqB;AAC9B,aAAK,MAAM,MAAM;AAAA,MACnB;AAAA,IACF,CAAC;AACD,QAAI,KAAK,oBAAoB;AAG3B,WAAK,mBAAmB,eAAe,EAAE,UAAU,MAAM;AACvD,oBAAY,kBAAkB,MAAM;AAAA,MACtC,CAAC;AACD,WAAK,mBAAmB,QAAQ;AAAA,IAClC,OAAO;AAEL,kBAAY,kBAAkB,MAAM;AAAA,IACtC;AAEA,QAAI,OAAO,YAAY,OAAO,WAAW,GAAG;AAC1C,kBAAY,YAAY,EAAE,UAAU,MAAM,YAAY,cAAc,OAAO,QAAQ,CAAC;AAAA,IACtF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,QAAQ;AACrB,UAAM,gBAAgB,IAAI,cAAc;AACxC,kBAAc,YAAY,OAAO;AACjC,QAAI,mBAAmB,KAAK,SAAS,SAAS,EAAE,OAAO;AAEvD,UAAM,QAAQ,OAAO,cAAc;AACnC,UAAM,SAAS,OAAO,uBAAuB,UAAU,OAAO,uBAAuB,WAAW,CAAC,SAAS,OAAO,uBAAuB,SAAS;AACjJ,UAAM,UAAU,CAAC,UAAU,OAAO,uBAAuB;AACzD,QAAI,QAAQ;AACV,uBAAiB,KAAK,GAAG;AAAA,IAC3B,WAAW,SAAS;AAClB,uBAAiB,MAAM,GAAG;AAAA,IAC5B,OAAO;AACL,uBAAiB,mBAAmB;AAAA,IACtC;AAEA,QAAI,OAAO,qBAAqB,OAAO;AACrC,uBAAiB,IAAI,GAAG;AAAA,IAC1B,OAAO;AACL,uBAAiB,OAAO,GAAG;AAAA,IAC7B;AACA,kBAAc,mBAAmB;AACjC,WAAO,KAAK,SAAS,OAAO,aAAa;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,QAAQ,aAAa;AACnC,UAAM,eAAe,UAAU,OAAO,oBAAoB,OAAO,iBAAiB;AAClF,WAAO,SAAS,OAAO;AAAA,MACrB,QAAQ,gBAAgB,KAAK;AAAA,MAC7B,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU,OAAO;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,GAAG;AAC1C,aAAO,KAAK,KAAK,cAAgB,SAAc,OAAO,GAAM,SAAc,aAAa,GAAM,SAAY,QAAQ,GAAM,SAAc,kBAAkB,GAAM,SAAS,cAAa,EAAE,GAAM,SAAS,6BAA6B,CAAC;AAAA,IACpO;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,aAAY;AAAA,MACrB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,aAAa,CAAC,sBAAsB,kBAAkB,oBAAoB,iBAAiB;AACjG,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAmB;AAAA,IACtC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,eAAe,cAAc,iBAAiB,iBAAiB,gBAAgB,sBAAsB,kBAAkB,oBAAoB,iBAAiB;AAAA,MACtK,SAAS,CAAC,iBAAiB,sBAAsB,kBAAkB,oBAAoB,iBAAiB;AAAA,IAC1G,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,WAAW;AAAA,MACvB,SAAS,CAAC,eAAe,cAAc,iBAAiB,iBAAiB,gBAAgB,sBAAsB,eAAe;AAAA,IAChI,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe,cAAc,iBAAiB,iBAAiB,gBAAgB,GAAG,UAAU;AAAA,MACtG,SAAS,CAAC,iBAAiB,GAAG,UAAU;AAAA,MACxC,WAAW,CAAC,WAAW;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["state"]}