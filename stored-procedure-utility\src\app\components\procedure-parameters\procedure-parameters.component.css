.parameters-card, .placeholder-card {
  margin: 20px;
  max-width: 1200px;
}

.loading-container, .error-container, .no-parameters {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  text-align: center;
}

.loading-container p, .no-parameters p {
  margin-top: 16px;
  color: #666;
}

.error-container {
  color: #d32f2f;
}

.error-container p {
  margin-top: 16px;
}

.error-container mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
}

.no-parameters mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #666;
}

.parameters-container {
  margin-top: 20px;
}

.parameters-table {
  width: 100%;
}

.parameters-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.parameters-table td {
  padding: 12px 8px;
}

.parameter-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.parameter-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.nullable-indicator, .output-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nullable-icon, .output-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.default-value {
  font-style: italic;
  color: #666;
}

.parameter-summary {
  display: flex;
  gap: 24px;
  margin-top: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
}

.summary-item mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
  text-align: center;
  color: #666;
}

.placeholder-content mat-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  color: #ccc;
}

.placeholder-content h3 {
  margin: 16px 0 8px 0;
  color: #333;
}

.placeholder-content p {
  margin: 0;
  max-width: 400px;
}

/* Responsive design */
@media (max-width: 768px) {
  .parameters-table {
    font-size: 14px;
  }
  
  .parameters-table td, .parameters-table th {
    padding: 8px 4px;
  }
  
  .parameter-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .parameter-name {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .nullable-indicator, .output-indicator {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
