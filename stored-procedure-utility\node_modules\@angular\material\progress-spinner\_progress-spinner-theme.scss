@use 'sass:map';
@use '../core/style/sass-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/tokens/m2/mdc/circular-progress' as tokens-mdc-circular-progress;
@use '@material/circular-progress/circular-progress-theme' as mdc-circular-progress-theme;

@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {
    // Add default values for tokens not related to color, typography, or density.
    @include sass-utils.current-selector-or-root() {
      @include mdc-circular-progress-theme.theme(
          tokens-mdc-circular-progress.get-unthemable-tokens()
      );
    }
  }
}

@mixin color($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color));
  }
  @else {
    $mdc-circular-progress-color-tokens: tokens-mdc-circular-progress.get-color-tokens($theme);

    @include sass-utils.current-selector-or-root() {
      @include mdc-circular-progress-theme.theme($mdc-circular-progress-color-tokens);

      .mat-accent {
        $color: inspection.get-theme-color($theme, accent);
        @include mdc-circular-progress-theme.theme((active-indicator-color: $color));
      }

      .mat-warn {
        $color: inspection.get-theme-color($theme, warn);
        @include mdc-circular-progress-theme.theme((active-indicator-color: $color));
      }
    }
  }
}

@mixin typography($theme) {
}

@mixin density($theme) {
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-progress-spinner') {
    @include base($theme);
    @if inspection.theme-has($theme, color) {
      @include color($theme);
    }
    @if inspection.theme-has($theme, density) {
      @include density($theme);
    }
    @if inspection.theme-has($theme, typography) {
      @include typography($theme);
    }
  }
}

@mixin _theme-from-tokens($tokens) {
  @if ($tokens != ()) {
    @include mdc-circular-progress-theme.theme(
        map.get($tokens, tokens-mdc-circular-progress.$prefix));
  }
}
