export interface StoredProcedure {
  id: number;
  name: string;
  schema: string;
  fullName: string;
  description?: string;
  created: Date;
  modified: Date;
}

export interface ProcedureParameter {
  id: number;
  procedureId: number;
  name: string;
  dataType: string;
  maxLength?: number;
  precision?: number;
  scale?: number;
  isOutput: boolean;
  isNullable: boolean;
  defaultValue?: string;
  ordinalPosition: number;
}

export interface ExcelColumn {
  name: string;
  index: number;
  dataType: string;
  sampleValues: string[];
}

export interface ParameterMapping {
  parameter: ProcedureParameter;
  excelColumn?: ExcelColumn;
  staticValue?: string;
  mappingType: 'column' | 'static' | 'none';
}

export interface ExcelData {
  fileName: string;
  worksheetName: string;
  columns: ExcelColumn[];
  rows: any[][];
  totalRows: number;
}

export interface ExecutionResult {
  rowIndex: number;
  success: boolean;
  message?: string;
  error?: string;
  executionTime: number;
  result?: any;
}

export interface ExecutionSummary {
  totalRows: number;
  successCount: number;
  errorCount: number;
  totalExecutionTime: number;
  results: ExecutionResult[];
}
