/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export * from './sidenav-module';
export { throwMatDuplicatedDrawerError, MAT_DRAWER_DEFAULT_AUTOSIZE, MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY, MatDrawerContent, MatDrawer, MatDrawerContainer, } from './drawer';
export * from './sidenav';
export * from './drawer-animations';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHVibGljLWFwaS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uL3NyYy9tYXRlcmlhbC9zaWRlbmF2L3B1YmxpYy1hcGkudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsY0FBYyxrQkFBa0IsQ0FBQztBQUNqQyxPQUFPLEVBQ0wsNkJBQTZCLEVBRTdCLDJCQUEyQixFQUMzQixtQ0FBbUMsRUFDbkMsZ0JBQWdCLEVBQ2hCLFNBQVMsRUFDVCxrQkFBa0IsR0FFbkIsTUFBTSxVQUFVLENBQUM7QUFDbEIsY0FBYyxXQUFXLENBQUM7QUFDMUIsY0FBYyxxQkFBcUIsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5leHBvcnQgKiBmcm9tICcuL3NpZGVuYXYtbW9kdWxlJztcbmV4cG9ydCB7XG4gIHRocm93TWF0RHVwbGljYXRlZERyYXdlckVycm9yLFxuICBNYXREcmF3ZXJUb2dnbGVSZXN1bHQsXG4gIE1BVF9EUkFXRVJfREVGQVVMVF9BVVRPU0laRSxcbiAgTUFUX0RSQVdFUl9ERUZBVUxUX0FVVE9TSVpFX0ZBQ1RPUlksXG4gIE1hdERyYXdlckNvbnRlbnQsXG4gIE1hdERyYXdlcixcbiAgTWF0RHJhd2VyQ29udGFpbmVyLFxuICBNYXREcmF3ZXJNb2RlLFxufSBmcm9tICcuL2RyYXdlcic7XG5leHBvcnQgKiBmcm9tICcuL3NpZGVuYXYnO1xuZXhwb3J0ICogZnJvbSAnLi9kcmF3ZXItYW5pbWF0aW9ucyc7XG4iXX0=