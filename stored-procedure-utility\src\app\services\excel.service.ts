import { Injectable } from '@angular/core';
import { Observable, from } from 'rxjs';
import { map } from 'rxjs/operators';
import * as XLSX from 'xlsx';
import { ExcelData, ExcelColumn } from '../models/stored-procedure.model';

@Injectable({
  providedIn: 'root'
})
export class ExcelService {

  constructor() { }

  // Parse Excel file and return structured data
  parseExcelFile(file: File): Observable<ExcelData> {
    return from(this.readFileAsArrayBuffer(file)).pipe(
      map(arrayBuffer => {
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });
        const worksheetName = workbook.SheetNames[0]; // Use first worksheet
        const worksheet = workbook.Sheets[worksheetName];
        
        // Convert worksheet to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
          header: 1, // Use array of arrays format
          defval: '' // Default value for empty cells
        }) as any[][];

        if (jsonData.length === 0) {
          throw new Error('Excel file is empty');
        }

        // Extract headers (first row)
        const headers = jsonData[0] as string[];
        const dataRows = jsonData.slice(1);

        // Create column definitions
        const columns: ExcelColumn[] = headers.map((header, index) => {
          const sampleValues = dataRows
            .slice(0, 5) // Take first 5 rows for samples
            .map(row => row[index])
            .filter(value => value !== null && value !== undefined && value !== '')
            .map(value => String(value));

          return {
            name: header || `Column_${index + 1}`,
            index: index,
            dataType: this.inferDataType(sampleValues),
            sampleValues: sampleValues
          };
        });

        const excelData: ExcelData = {
          fileName: file.name,
          worksheetName: worksheetName,
          columns: columns,
          rows: dataRows,
          totalRows: dataRows.length
        };

        return excelData;
      })
    );
  }

  // Read file as ArrayBuffer
  private readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          resolve(e.target.result as ArrayBuffer);
        } else {
          reject(new Error('Failed to read file'));
        }
      };
      reader.onerror = () => reject(new Error('Error reading file'));
      reader.readAsArrayBuffer(file);
    });
  }

  // Infer data type from sample values
  private inferDataType(sampleValues: string[]): string {
    if (sampleValues.length === 0) {
      return 'string';
    }

    let numberCount = 0;
    let dateCount = 0;
    let booleanCount = 0;

    for (const value of sampleValues) {
      // Check if it's a number
      if (!isNaN(Number(value)) && value.trim() !== '') {
        numberCount++;
        continue;
      }

      // Check if it's a date
      const dateValue = new Date(value);
      if (!isNaN(dateValue.getTime()) && value.includes('/') || value.includes('-')) {
        dateCount++;
        continue;
      }

      // Check if it's a boolean
      const lowerValue = value.toLowerCase();
      if (lowerValue === 'true' || lowerValue === 'false' || 
          lowerValue === 'yes' || lowerValue === 'no' ||
          lowerValue === '1' || lowerValue === '0') {
        booleanCount++;
        continue;
      }
    }

    const total = sampleValues.length;
    const numberRatio = numberCount / total;
    const dateRatio = dateCount / total;
    const booleanRatio = booleanCount / total;

    // If more than 80% of values match a type, consider it that type
    if (numberRatio > 0.8) {
      return 'number';
    } else if (dateRatio > 0.8) {
      return 'date';
    } else if (booleanRatio > 0.8) {
      return 'boolean';
    } else {
      return 'string';
    }
  }

  // Validate Excel file
  validateExcelFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv' // .csv
    ];

    if (!validTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls|csv)$/i)) {
      return {
        isValid: false,
        error: 'Invalid file type. Please select an Excel file (.xlsx, .xls) or CSV file.'
      };
    }

    // Check file size (limit to 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size too large. Please select a file smaller than 10MB.'
      };
    }

    return { isValid: true };
  }

  // Export execution results to Excel
  exportResultsToExcel(results: any[], fileName: string = 'execution_results'): void {
    const worksheet = XLSX.utils.json_to_sheet(results);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Results');
    
    // Save file
    XLSX.writeFile(workbook, `${fileName}.xlsx`);
  }
}
