/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { getNoKeysSpecifiedError } from '@angular/cdk/testing';
import { PERIOD } from '@angular/cdk/keycodes';
import { dispatchFakeEvent, dispatchKeyboardEvent } from './dispatch-events';
import { triggerFocus } from './element-focus';
/** Input types for which the value can be entered incrementally. */
const incrementalInputTypes = new Set([
    'text',
    'email',
    'hidden',
    'password',
    'search',
    'tel',
    'url',
]);
/**
 * Checks whether the given Element is a text input element.
 * @docs-private
 */
export function isTextInput(element) {
    const nodeName = element.nodeName.toLowerCase();
    return nodeName === 'input' || nodeName === 'textarea';
}
export function typeInElement(element, ...modifiersAndKeys) {
    const first = modifiersAndKeys[0];
    let modifiers;
    let rest;
    if (first !== undefined &&
        typeof first !== 'string' &&
        first.keyCode === undefined &&
        first.key === undefined) {
        modifiers = first;
        rest = modifiersAndKeys.slice(1);
    }
    else {
        modifiers = {};
        rest = modifiersAndKeys;
    }
    const isInput = isTextInput(element);
    const inputType = element.getAttribute('type') || 'text';
    const keys = rest
        .map(k => typeof k === 'string'
        ? k.split('').map(c => ({ keyCode: c.toUpperCase().charCodeAt(0), key: c }))
        : [k])
        .reduce((arr, k) => arr.concat(k), []);
    // Throw an error if no keys have been specified. Calling this function with no
    // keys should not result in a focus event being dispatched unexpectedly.
    if (keys.length === 0) {
        throw getNoKeysSpecifiedError();
    }
    // We simulate the user typing in a value by incrementally assigning the value below. The problem
    // is that for some input types, the browser won't allow for an invalid value to be set via the
    // `value` property which will always be the case when going character-by-character. If we detect
    // such an input, we have to set the value all at once or listeners to the `input` event (e.g.
    // the `ReactiveFormsModule` uses such an approach) won't receive the correct value.
    const enterValueIncrementally = inputType === 'number'
        ? // The value can be set character by character in number inputs if it doesn't have any decimals.
            keys.every(key => key.key !== '.' && key.key !== '-' && key.keyCode !== PERIOD)
        : incrementalInputTypes.has(inputType);
    triggerFocus(element);
    // When we aren't entering the value incrementally, assign it all at once ahead
    // of time so that any listeners to the key events below will have access to it.
    if (!enterValueIncrementally) {
        element.value = keys.reduce((value, key) => value + (key.key || ''), '');
    }
    for (const key of keys) {
        dispatchKeyboardEvent(element, 'keydown', key.keyCode, key.key, modifiers);
        dispatchKeyboardEvent(element, 'keypress', key.keyCode, key.key, modifiers);
        if (isInput && key.key && key.key.length === 1) {
            if (enterValueIncrementally) {
                element.value += key.key;
                dispatchFakeEvent(element, 'input');
            }
        }
        dispatchKeyboardEvent(element, 'keyup', key.keyCode, key.key, modifiers);
    }
    // Since we weren't dispatching `input` events while sending the keys, we have to do it now.
    if (!enterValueIncrementally) {
        dispatchFakeEvent(element, 'input');
    }
}
/**
 * Clears the text in an input or textarea element.
 * @docs-private
 */
export function clearElement(element) {
    triggerFocus(element);
    element.value = '';
    dispatchFakeEvent(element, 'input');
}
//# sourceMappingURL=data:application/json;base64,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