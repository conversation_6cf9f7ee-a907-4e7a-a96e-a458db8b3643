﻿app.controller('FollowUpPurchase_Ctrl', function ($scope, $rootScope, HMIS_Service, $mdDialog, $routeParams, $mdToast, Generic_Service, $filter) {

    var caller = '#!FollowUpPurchase';
    var Departments;
    function prepareObject_FollowUpPurchase() {
        debugger
        var followuppurchase = {};

        followuppurchase.ID = $scope.FollowUpPurchase.ID;
        // كود المستند
        followuppurchase.DocumentCode = $scope.FollowUpPurchase.DocumentCode;
        // التاريخ
        followuppurchase.DateTime =  $rootScope.FormatedDateTime($scope.FollowUpPurchase.DateTime);
        //followuppurchase.DateTime = $scope.FollowUpPurchase.DateTime;
        // كود مستند مريض
        //followuppurchase.PatientDocumentCode = $scope.FollowUpPurchase.PatientDocumentCode;
        // ملاحظات
        followuppurchase.Remarks = $scope.FollowUpPurchase.Remarks;
        // المريض
        followuppurchase.PatientID = ($scope.Reception.Patient != null ? $scope.Reception.Patient.ID : null);
        // استقبال مريض
        followuppurchase.ReceptionID = ($scope.Reception != null ? $scope.Reception.ID : null);
        // نوع المتابعة
        followuppurchase.XFollowUpTypeID = ($scope.FollowUpPurchase.XFollowUpType != null ? $scope.FollowUpPurchase.XFollowUpType.ID : null);
        // المتابعة
        followuppurchase.PackageID = ($scope.FollowUpPurchase.Package != null ? $scope.FollowUpPurchase.Package.ID : null);
        // تاريخ البداية
        followuppurchase.StartDate =  $rootScope.FormatedDate($scope.FollowUpPurchase.StartDate);
        //followuppurchase.StartDate = $scope.FollowUpPurchase.StartDate;
        // تاريخ النهاية
        followuppurchase.EndDate =  $rootScope.FormatedDate($scope.FollowUpPurchase.EndDate);
        //followuppurchase.EndDate = $scope.FollowUpPurchase.EndDate;
        if (followuppurchase.XFollowUpTypeID == 302002) {
        // الأيام
            followuppurchase.DayCount = $scope.FollowUpPurchase.Package.DaysLimit;
        // الشهور
            followuppurchase.MonthCount = $scope.FollowUpPurchase.Package.MonthsLimit;
        // عدد الزيارات
            followuppurchase.VisitsCount = $scope.FollowUpPurchase.Package.VisitsCount;
        }
        if (followuppurchase.XFollowUpTypeID == 302001) {
            // الأيام
            followuppurchase.DayCount = $scope.FollowUpPurchase.DayCount;
            // الشهور
            followuppurchase.MonthCount = $scope.FollowUpPurchase.MonthCount;
            // عدد الزيارات
            followuppurchase.VisitsCount = $scope.FollowUpPurchase.VisitsCount;
        }
        // السعر
        followuppurchase.Price = $scope.FollowUpPurchase.Price;
        // نسبة خصم الجهة
        followuppurchase.OrgDiscountPercent = $scope.FollowUpPurchase.OrgDiscountPercent;
        // قيمة خصم الجهة
        followuppurchase.OrgDiscountValue = $scope.FollowUpPurchase.OrgDiscountValue;
        // نسبة تحمل المريض
        followuppurchase.PatientPercent = $scope.FollowUpPurchase.PatientPercent;
        // نسبة تحمل الجهة
        followuppurchase.OrgPercent = $scope.FollowUpPurchase.OrgPercent;
        // ما يتحمله المريض
        if ($scope.FollowUpPurchase.PatientValue == null) {
        followuppurchase.PatientValue = $scope.FollowUpPurchase.Price;
    }else{
            followuppurchase.PatientValue = $scope.FollowUpPurchase.PatientValue;
    }
        // ما تتحمله الجهة
        followuppurchase.OrgValue = $scope.FollowUpPurchase.OrgValue;
        // نسبة خصم المريض
        followuppurchase.PatientDiscountPercent = $scope.FollowUpPurchase.PatientDiscountPercent;
        // قيمة خصم المريض
        followuppurchase.PatientDiscount = $scope.FollowUpPurchase.PatientDiscount;
        // الباقي
        followuppurchase.Remaining = $scope.FollowUpPurchase.Remaining;
        // المدفوع كاش
        followuppurchase.CashPaid = $scope.FollowUpPurchase.CashPaid;
        // المدفوع فيزا
        followuppurchase.VisaPaid = $scope.FollowUpPurchase.VisaPaid;
        // إجمالي المدفوع مقدما
        followuppurchase.TotalAdvance = $scope.FollowUpPurchase.TotalAdvance;
        //قيمة بعد الخصم
        followuppurchase.PatientValueAfterDiscount = $scope.FollowUpPurchase.PatientValueAfterDiscount;
        // إجمالي المدفوع
        followuppurchase.TotalPaid = $scope.FollowUpPurchase.TotalAdvance;
        // 3. ClinicID
        followuppurchase.ClinicID = ($scope.FollowUpPurchase.Clinic != null ? $scope.FollowUpPurchase.Clinic.ID : null);
        // رقم الفيزا
        followuppurchase.VisaNumber = $scope.FollowUpPurchase.VisaNumber;
        // 3. BankID
        followuppurchase.BankID = ($scope.FollowUpPurchase.Bank != null ? $scope.FollowUpPurchase.Bank.ID : null);
        //السعر بعد الخصم للجهه
        followuppurchase.OrgDiscountPrice = $scope.FollowUpPurchase.OrgDiscountPrice;
        

        if ($rootScope.CompanyAuthority.Branches == true) {
            if (followuppurchase.ClinicID != null) {
                args = {};
                args.Name = 'X_GetObjectBranch';
                args.Params = [
                     { Name: 'ID', Value: followuppurchase.ClinicID }
                ];
                var Result = HMIS_Service.GetObject('Generic', args);
                if (Result != null) {
                    $scope.FollowUpPurchase.BranchID = Result.BranchID;
                }
            }
            followuppurchase.BranchID = $scope.FollowUpPurchase.BranchID;
        }


        // FollowUpPurchaseService
        followuppurchase.FollowUpPurchaseServices = [];
        if ($scope.FollowUpPurchase.FollowUpPurchaseServices != null && $scope.FollowUpPurchase.FollowUpPurchaseServices.length > 0) {
            angular.forEach($scope.FollowUpPurchase.FollowUpPurchaseServices, function (_item, i) {
                var newItem = {};
                // ID 
                newItem.ID = _item.ID;
                // Status 
                newItem.Status = _item.Status;
                // نوع الخدمة
                if (_item.XServiceType != null) {
                    newItem.XServiceTypeID = _item.XServiceType.ID;
                }
                // الخدمة
                if (_item.Service != null) {
                    newItem.ServiceID = _item.Service.ID;
                }
                // عدد
                newItem.ServiceCount = _item.ServiceCount;
                // السعر
                newItem.Price = _item.Price;

                followuppurchase.FollowUpPurchaseServices.push(newItem);
            });
        }
        return followuppurchase;
    }

    function prepare_Reception() {
        debugger
        var reception = {};
        reception.addToMedicore = $scope.addToMedicore;
        reception.ID = $scope.Reception.ID;
        reception.DocumentCode = $scope.Reception.DocumentCode;
        reception.DateTime = new Date();

        reception.PatientID = ($scope.Reception.Patient != null ? $scope.Reception.Patient.ID : null);

        reception.VisitNo = $scope.Reception.VisitNo;
        reception.XVisitTypeID = ($scope.Reception.XVisitType != null ? $scope.Reception.XVisitType.ID : null);
        reception.DoctorID = ($scope.Reception.Doctor != null ? $scope.Reception.Doctor.ID : null);
        reception.XVisitPaymentTypeID = ($scope.Reception.XVisitPaymentType != null ? $scope.Reception.XVisitPaymentType.ID : null);
        reception.Amount = $scope.Reception.Amount;
        reception.CurrencyID = ($scope.Reception.Currency != null ? $scope.Reception.Currency.ID : null);
        reception.XPaymentMethodID = ($scope.Reception.XPaymentMethod != null ? $scope.Reception.XPaymentMethod.ID : null);
        reception.CashierID = ($scope.Reception.Cashier != null ? $scope.Reception.Cashier.ID : null);
        reception.ReferralDoctorID = ($scope.Reception.ReferralDoctor != null ? $scope.Reception.ReferralDoctor.ID : null);

        reception.XInsuranceMemberTypeID = ($scope.Reception.XInsuranceMemberType != null ? $scope.Reception.XInsuranceMemberType.ID : null);
        reception.PatientClassID = ($scope.Reception.PatientClass != null ? $scope.Reception.PatientClass.ID : null);

        reception.XAdmissionTypeID = ($scope.Reception.XAdmissionType != null ? $scope.Reception.XAdmissionType.ID : null);
        reception.XAmbulatoryStatusID = ($scope.Reception.XAmbulatoryStatus != null ? $scope.Reception.XAmbulatoryStatus.ID : null);
        reception.XAdmitSourceID = ($scope.Reception.XAdmitSource != null ? $scope.Reception.XAdmitSource.ID : null);

        reception.XClinicVisitTypeID = ($scope.Reception.XClinicVisitType != null ? $scope.Reception.XClinicVisitType.ID : null);

        // الطبيب
        reception.ExecutiveDoctorID = ($scope.Reception.ExecutiveDoctor != null ? $scope.Reception.ExecutiveDoctor.ID : null);

        // XDepID
        reception.XDepID = 153008;
        // DepartmentID
        reception.DepartmentID = ($scope.Reception.Department != null ? $scope.Reception.Department.ID : null);

        //reception.DepartmentID = 22386;

        //رقم الوثيقه
        reception.DocumentNum = $scope.Reception.DocumentNum;
        //رقم المعال
        reception.DependentNum = $scope.Reception.DependentNum;

        reception.InsuredCode = $scope.Reception.InsuredCode;
        reception.FamilyMember = $scope.Reception.FamilyMember;

        // data needed to complete reservation
        if (!$rootScope.isNull($scope.DeviceReservation)) {

            // 1. DateTableID
            reception.DateTableID = $scope.DeviceReservation.DateTable.ID;

            // 2 TimeTableID
            reception.TimeTableID = $scope.DeviceReservation.TimeTable.ID;

            // 3. DeviceID
            reception.DeviceID = $scope.DeviceReservation.Device.ID;
        }


        // data needed to complete reservation
        if (!$rootScope.isNull($scope.ClinicReservation)) {

            // 1. DateTableID
            reception.DateTableID = $scope.ClinicReservation.DateTable.ID;

            // 2 TimeTableID
            reception.TimeTableID = $scope.ClinicReservation.TimeTable.ID;

            // 3. ClinicID
            reception.ClinicID = $scope.ClinicReservation.Clinic.ID;

            if ($rootScope.CompanyAuthority.Branches == true) {
                //BranchID
                reception.BranchID = $scope.FollowUpPurchase.BranchID;
            }

            // 4. DoctorID
            reception.DoctorID = $scope.ClinicReservation.Doctor.ID;
        }

        $scope.SplitPatientName();

        var patient = {};
        patient.ID = $scope.Reception.Patient == undefined ? null : $scope.Reception.Patient.ID;
        patient.Code = $scope.Reception.Code;
        patient.PatientCode = $scope.Reception.Patient.PatientCode;
        patient.MRN = $scope.Reception.Patient.MRN;
        patient.FirstName = $scope.Reception.Patient.FirstName;
        patient.SecondName = $scope.Reception.Patient.SecondName;
        patient.ThirdName = $scope.Reception.Patient.ThirdName;
        patient.ForthName = $scope.Reception.Patient.ForthName;
        //إسم المريض انجليزى
        patient.PatientEnglishName = $scope.Reception.Patient.PatientEnglishName;
        patient.Name = $scope.Reception.Patient.Name;
        patient.XGenderID = ($scope.Reception.Patient.XGender != null ? $scope.Reception.Patient.XGender.ID : null);
        patient.XMaritalStatusID = ($scope.Reception.Patient.XMaritalStatus != null ? $scope.Reception.Patient.XMaritalStatus.ID : null);
        patient.NationalityID = ($scope.Reception.Patient.Nationality != null ? $scope.Reception.Patient.Nationality.ID : null);
        patient.Passport = $scope.Reception.Passport;
        patient.StayDocumentNum = $scope.Reception.StayDocumentNum;
        patient.NationalID = $scope.Reception.Patient.NationalID;
        patient.Address = $scope.Reception.Patient.Address;
        patient.BirthDate = $rootScope.FormatedDate($scope.Reception.Patient.BirthDate);

        // CountryID
        if (!$rootScope.isNull($scope.Reception.Patient) && !$rootScope.isNull($scope.Reception.Patient.Country)) {
            patient.CountryID = $scope.Reception.Patient.Country.ID;
        }

        // GovernorID
        if (!$rootScope.isNull($scope.Reception.Patient) && !$rootScope.isNull($scope.Reception.Patient.Governor)) {
            patient.GovernorID = $scope.Reception.Patient.Governor.ID;
        }

        // CityID
        if (!$rootScope.isNull($scope.Reception.Patient) && !$rootScope.isNull($scope.Reception.Patient.City)) {
            patient.CityID = $scope.Reception.Patient.City.ID;
        }

        // Reception.Patient.MotherName
        if (!$rootScope.isNull($scope.Reception.Patient) && !$rootScope.isNull($scope.Reception.Patient.MotherName)) {
            patient.MotherName = $scope.Reception.Patient.MotherName;
        }

        try {
            //$scope.Reception.Patient.BirthDate = $rootScope.FormatedDate($scope.Reception.Patient.BirthDate);
            patient.YearOfBirth = ($scope.Reception.Patient.BirthDate != null ? new Date($scope.Reception.Patient.BirthDate).getFullYear() : null);
            patient.DayOfBirth = ($scope.Reception.Patient.BirthDate != null ? $scope.Reception.Patient.BirthDate.getDate() : null);
            patient.XMonthOfBirthID = ($scope.Reception.Patient.BirthDate != null ? $scope.Reception.Patient.BirthDate.getMonth() + 134000 : null);
        } catch (e) {
            patient.YearOfBirth = null;
            patient.DayOfBirth = null;
            patient.XMonthOfBirthID = null;
        }

        //patient.YearOfBirth = $scope.Reception.Patient.BirthDate.getFullYear();
        //patient.DayOfBirth = $scope.Reception.Patient.BirthDate.getDate();
        //patient.XMonthOfBirthID = $scope.Reception.Patient.BirthDate.getMonth() + 134000;

        // وسائل إتصال
        patient.PersonContacts = [];

        if ($scope.PatientContact != null) {

            var newItem = {};
            // ID 
            newItem.ID = $scope.PatientContact.ID;
            // Status 
            newItem.Status = 1;
            // الهاتف
            newItem.Phone = $scope.PatientContact.Phone;
            // المحمول
            newItem.Mobile = $scope.PatientContact.Mobile;
            // البريد الإلكتروني
            newItem.EMail = $scope.PatientContact.EMail;
            // الفاكس
            newItem.Fax = $scope.PatientContact.Fax;
            // موقع على الانترنت
            newItem.Site = $scope.PatientContact.Site;

            patient.PersonContacts.push(newItem)
        }

        //if ($scope.Reception.Patient.PersonContacts != null && $scope.Reception.Patient.PersonContacts.length > 0) {
        //    angular.forEach($scope.Reception.Patient.PersonContacts, function (item, i) {
        //        var newItem = {};
        //        // ID 
        //        newItem.ID = item.ID;
        //        // Status 
        //        newItem.Status = item.Status;
        //        // الهاتف
        //        newItem.Phone = item.Phone;
        //        // المحمول
        //        newItem.Mobile = item.Mobile;
        //        // البريد الإلكتروني
        //        newItem.EMail = item.EMail;
        //        // الفاكس
        //        newItem.Fax = item.Fax;
        //        // موقع على الانترنت
        //        newItem.Site = item.Site;

        //        patient.PersonContacts.push(newItem)
        //    });
        //}

        reception.Patient = patient;

        // جهة تأمين المريض

        reception.PatientInsuranceCompanies = [];
        if ($scope.Reception.PatientInsuranceCompanies != null && $scope.Reception.PatientInsuranceCompanies.length > 0) {
            angular.forEach($scope.Reception.PatientInsuranceCompanies, function (item, i) {
                var newItem = {};
                // ID 
                newItem.ID = item.ID;
                // Status 
                newItem.Status = item.Status;
                // شركة التأمين
                if (item.InsuranceCompany != null) {
                    newItem.InsuranceCompanyID = item.InsuranceCompany.ID;
                }
                // عقد التأمين
                if (item.InsuranceContract != null) {
                    newItem.InsuranceContractID = item.InsuranceContract.ID;
                }
                // البوليصة
                if (item.Policy != null) {
                    newItem.PolicyID = item.Policy.ID;
                }
                // رقم الكارنيه
                newItem.CardNo = item.CardNo;
                //إسم العضو
                newItem.MemberName = item.MemberName;
                // شركة التأمين الأم
                if (item.MainInsuranceCompany != null) {
                    newItem.MainInsuranceCompanyID = item.MainInsuranceCompany.ID;
                }
                reception.PatientInsuranceCompanies.push(newItem)
            });
        }

        return reception;
    }

    function prepare_OutpatientPayment() {
        debugger
        var outpatientpayment = {};

        var treasuryTransactions =
            (
                !$rootScope.isNull($scope.OutpatientPayment)
                &&
                ($scope.FollowUpPurchase.CashPaid != null || $scope.FollowUpPurchase.CashPaid != 0 || $scope.FollowUpPurchase.VisaPaid != null || $scope.FollowUpPurchase.VisaPaid != 0)
                &&
                !$rootScope.isNull($scope.OutpatientPayment.Treasury)
                //&&
                //!$rootScope.isNull($scope.OutpatientPayment.Paid)
                //&&
                //$scope.OutpatientPayment.Paid != 0
            );

        if (treasuryTransactions) {

            //if (!$rootScope.isNull($scope.OutpatientPayment) && $scope.OutpatientPayment.Treasury != null && $scope.OutpatientPayment.Paid != null) {

            outpatientpayment.ID = $scope.OutpatientPayment.ID;
            outpatientpayment.DocumentCode = $scope.OutpatientPayment.DocumentCode;
            //outpatientpayment.DateTime = $scope.Reception.DateTime != undefined ? $scope.OutpatientPayment.DateTime.addDay() : null;
            outpatientpayment.DateTime = $rootScope.FormatedDateTime($scope.Reception.DateTime); //$rootScope.FormatedDateTime($scope.Reception.DateTime);
            //outpatientpayment.ServiceRequestID = ($scope.OutpatientPayment.ServiceRequest != null ? $scope.OutpatientPayment.ServiceRequest.ID : null);
            //outpatientpayment.ServiceRequestID = ($scope.FollowUpPurchase.ID);
            outpatientpayment.PatientID = ($scope.Reception.Patient != null ? $scope.Reception.Patient.ID : null);
            outpatientpayment.TotalRequested = $scope.OutpatientPayment.TotalRequested;
            outpatientpayment.PreviousPayment = (+parseFloat($scope.FollowUpPurchase.CashPaid == null ? 0 : $scope.FollowUpPurchase.CashPaid) + +parseFloat($scope.FollowUpPurchase.VisaPaid == null ? 0 : $scope.FollowUpPurchase.VisaPaid));
            outpatientpayment.Paid = (+parseFloat($scope.FollowUpPurchase.CashPaid == null ? 0 : $scope.FollowUpPurchase.CashPaid) + +parseFloat($scope.FollowUpPurchase.VisaPaid == null ? 0 : $scope.FollowUpPurchase.VisaPaid));
            outpatientpayment.TreasuryID = ($scope.OutpatientPayment.Treasury != null ? $scope.OutpatientPayment.Treasury.ID : null);
            outpatientpayment.Remarks = $scope.OutpatientPayment.Remarks;

            // حركة خزينة
            outpatientpayment.TreasuryTransactions = [];
            if ($scope.FollowUpPurchase.CashPaid != null && $scope.FollowUpPurchase.CashPaid != 0 && $scope.FollowUpPurchase.CashPaid != undefined
                || $scope.FollowUpPurchase.VisaPaid != null && $scope.FollowUpPurchase.VisaPaid != 0 && $scope.FollowUpPurchase.VisaPaid != undefined) {
                if ($scope.OutpatientPayment.ID == null){
                $scope.add_TreasuryTransaction();
                if ($scope.FollowUpPurchase.CashPaid != null && $scope.FollowUpPurchase.CashPaid != 0 && $scope.FollowUpPurchase.CashPaid != undefined) {
                    angular.forEach($scope.OutpatientPayment.TreasuryTransactions, function (item, i) {
                        var newItem = {};
                        // ID 
                        newItem.ID = item.ID;
                        newItem.TreasuryID = outpatientpayment.TreasuryID;
                        // Status 
                        newItem.Status = 1;
                        //
                        newItem.NumberSign = 1;
                        // مبلغ
                        newItem.Value = $scope.FollowUpPurchase.CashPaid;
                        // العملة
                        newItem.CurrencyID = $scope.Currencies[0].ID;
                        // طرق الدفع
                        newItem.XPaymentMethodID = 113001;

                        outpatientpayment.TreasuryTransactions.push(newItem);
                    });
                }
                if ($scope.FollowUpPurchase.VisaPaid != null && $scope.FollowUpPurchase.VisaPaid != 0 && $scope.FollowUpPurchase.VisaPaid != undefined) {
                    //$scope.add_TreasuryTransaction();
                    angular.forEach($scope.OutpatientPayment.TreasuryTransactions, function (item, i) {
                        var newItem = {};
                        // ID 
                        newItem.ID = item.ID;
                        newItem.TreasuryID = outpatientpayment.TreasuryID;
                        // Status 
                        newItem.Status = 1;
                        //
                        newItem.NumberSign = 1;
                        // مبلغ
                        newItem.Value = $scope.FollowUpPurchase.VisaPaid;
                        // العملة
                        newItem.CurrencyID = $scope.Currencies[0].ID;
                        // طرق الدفع
                        newItem.XPaymentMethodID = 113002;
                        // رقم الفيزا
                        newItem.VisaNo = $scope.FollowUpPurchase.VisaNumber;
                        // اسم البنك
                        if ($scope.FollowUpPurchase.Bank != null) {
                            newItem.BankName = $scope.FollowUpPurchase.Bank.Name;
                        }
                        // البنك
                        newItem.BankID = $scope.FollowUpPurchase.BankID;

                        outpatientpayment.TreasuryTransactions.push(newItem);
                    });
                }
                }
                if ($scope.OutpatientPayment.ID != null) {
                    if ($scope.FollowUpPurchase.CashPaid != null && $scope.FollowUpPurchase.CashPaid != 0 && $scope.FollowUpPurchase.CashPaid != undefined) {
                        angular.forEach($scope.OutpatientPayment.TreasuryTransactions, function (item, i) {
                            var newItem = {};
                            // ID 
                            newItem.ID = item.ID;
                            newItem.TreasuryID = outpatientpayment.TreasuryID;
                            // Status 
                            newItem.Status = null;
                            //
                            newItem.NumberSign = 1;
                            // مبلغ
                            newItem.Value = $scope.FollowUpPurchase.CashPaid;
                            // العملة
                            newItem.CurrencyID = $scope.Currencies[0].ID;
                            // طرق الدفع
                            newItem.XPaymentMethodID = 113001;

                            outpatientpayment.TreasuryTransactions.push(newItem);
                        });
                    }
                    if ($scope.FollowUpPurchase.VisaPaid != null && $scope.FollowUpPurchase.VisaPaid != 0 && $scope.FollowUpPurchase.VisaPaid != undefined) {
                        angular.forEach($scope.OutpatientPayment.TreasuryTransactions, function (item, i) {
                            var newItem = {};
                            // ID 
                            newItem.ID = item.ID;
                            newItem.TreasuryID = outpatientpayment.TreasuryID;
                            // Status 
                            newItem.Status = null;
                            //
                            newItem.NumberSign = 1;
                            // مبلغ
                            newItem.Value = $scope.FollowUpPurchase.VisaPaid;
                            // العملة
                            newItem.CurrencyID = $scope.Currencies[0].ID;
                            // طرق الدفع
                            newItem.XPaymentMethodID = 113002;
                            // رقم الفيزا
                            newItem.VisaNo = $scope.FollowUpPurchase.VisaNumber;
                            // اسم البنك
                            if ($scope.FollowUpPurchase.Bank != null) {
                                newItem.BankName = $scope.FollowUpPurchase.Bank.Name;
                            }
                            // البنك
                            newItem.BankID = $scope.FollowUpPurchase.BankID;

                            outpatientpayment.TreasuryTransactions.push(newItem);
                        });
                    }
                }
            }

        }
        else {
            outpatientpayment == null;
        }
        return outpatientpayment;

    }

    $scope.minDate = new Date().toISOString().substring(0, 10) + 'T00:00:00';

    $scope.save = function(action) {
        debugger
        var OutpatientReception = {};

        // prepare FollowUpPurchase
        OutpatientReception.FollowUpPurchase = prepareObject_FollowUpPurchase();

        // prepare Reception
        OutpatientReception.Reception = prepare_Reception();

        // prepare OutpatientPayment
        OutpatientReception.OutpatientPayment = prepare_OutpatientPayment();

        if ($rootScope.CompanyAuthority.Branches == true) {
            OutpatientReception.Reception.BranchID = OutpatientReception.FollowUpPurchase.BranchID;
            OutpatientReception.OutpatientPayment.BranchID = OutpatientReception.FollowUpPurchase.BranchID;
        }

        if ($scope.FollowUpPurchase.ID == null) {
            OutpatientReception = HMIS_Service.Post('OutpatientFollowUpPurchase', OutpatientReception, $scope, caller, action, $mdDialog);
        }
        else {
            OutpatientReception = HMIS_Service.Put('OutpatientFollowUpPurchase', OutpatientReception, $scope, caller, action, $mdDialog);
        }
        //$mdDialog.hide(OutpatientReception.FollowUpPurchase, { closeAll: true });

    }

    $scope.getById = function (ID) {
        debugger
        $scope.FollowUpPurchase = HMIS_Service.Get('FollowUpPurchase', ID);
        //$scope.FollowUpPurchase = HMIS_Service.Get('OutpatientFollowUpPurchase', ID);
        $scope.Reception = HMIS_Service.Get('Reception', $scope.FollowUpPurchase.ReceptionID);

        $rootScope.Reception = $scope.Reception;

        // Patient Contact
        //getPatientContact($scope.Reception.PatientID);

        // get Outpatient Payments By Reception
        $scope.Payments = $rootScope.getOutpatientPaymentByFollowUpPurchase(null, ID);

        // display first OutpatientPayment bu default
        if (!$rootScope.isNull($scope.Payments)) {
            var OutpatientPaymentID = $scope.Payments[0].ID;
            //$scope.OutpatientPayment = $scope.getOutpatientPaymentById(OutpatientPaymentID);
            $scope.OutpatientPayment = HMIS_Service.Get('OutpatientPayment', OutpatientPaymentID);
            var Treasury = $scope.OutpatientPayment.Treasury;
            $scope.OutpatientPayment.Treasury = { ID: Treasury.ID, Code: Treasury.Code, Name: Treasury.Name };
            console.info('Treasury', $scope.OutpatientPayment.Treasury);
        }

        //if ($scope.Reception.XVisitPaymentTypeID == 149002) {
        getPatientInsuranceCompaniesCount();
        //}

        $scope.calcAge();
        if ($scope.FollowUpPurchase.XFollowUpType.ID == 302002) {
            var Pack = $scope.PackageName_Autocomplete();
            $scope.getPackageName_select(Pack[0])

        }

        if (!$rootScope.isNull($scope.Reception) &&
            !$rootScope.isNull($scope.Reception.PatientInsuranceCompanies)) {
            var ContractID = $scope.Reception.PatientInsuranceCompanies[0].InsuranceContract.ID
            ContractTypeNotification(ContractID);
        }

        if ($scope.FollowUpPurchase.PackageID == null) {
            $scope.FollowUpPurchase.Package = null;
        }

        getFollowUpPurchaseServicesCount();
    }

    function getServicesRequestsByReception(receptionID) {
        //X_ServiceRequest_ByReception
        var args = {};
        args.Name = 'X_ServiceRequest_ByReception';
        args.Params = [
            { Name: 'ReceptionID', Value: receptionID }
        ];
        $scope.Requests = HMIS_Service.Post('Generic', args);
    }

    function getPatientContact(PatientID) {
        // PersonContacts_ByPersonID
        args = {};
        args.Name = 'PersonContacts_ByPersonID';
        args.Params = [{ Name: 'PersonID', Value: PatientID }];
        var Contacts = HMIS_Service.Post('Generic', args);
        if (!$rootScope.isNull(Contacts)) {
            // Patient Contacts
            $scope.Reception.Patient.PersonContacts = Contacts;
            $scope.PatientContact = Contacts[0];
        }
    }

    $scope.Clinic_Autocomplete = function (SearchText, extraCondition) {
        debugger
        var extraCondition;
        if ($rootScope.isNull(extraCondition)) {
            extraCondition = '';
        }
        //extraCondition += 'And (isnull(XObjectStatusID, 0) <> 224002)';
        var data = HMIS_Service.AutoCompelete('Clinic_FN()', 'ID, Code, Name, Specialization_ID, Specialization_Name', 'Code, Name', '%' + SearchText + '%', extraCondition);
        return data;
    }
    $scope.Clinic_select = function (item) {
        if (item) {
            $scope.FollowUpPurchase.Clinic = item;
        } else {
            $scope.XFollowUpType_changed();
        }
    }

    $scope.new = function() {
        $scope.FollowUpPurchase = {};
        $scope.Reception = {};
        $scope.OutpatientPayment = {};

        $scope.PatientHasOpenedStay = null;

        //$scope.XVisitTypes = HMIS_Service.Get('LoadXFile', XFile.VisitType);
        //$scope.XVisitPaymentTypes = HMIS_Service.LoadXFile(XFile.VisitPaymentType, '149001,149002');
        //$scope.XClinicVisitTypes = HMIS_Service.Get('LoadXFile', XFile.ClinicVisitType);
        //$scope.Currencies = HMIS_Service.Get('Currency');
        //$scope.XPaymentMethods = HMIS_Service.Get('LoadXFile', XFile.PaymentMethod);
        //$scope.Cashiers = HMIS_Service.Get('Cashier');
        //$scope.XInsuranceMemberTypes = HMIS_Service.Get('LoadXFile', XFile.InsuranceMemberType);
        //$scope.PatientClasses = HMIS_Service.Get('PatientClass');
        //$scope.XAdmissionTypes = HMIS_Service.Get('LoadXFile', XFile.AdmissionType);
        //$scope.XAmbulatoryStatuses = HMIS_Service.Get('LoadXFile', XFile.AmbulatoryStatus);
        //$scope.XAdmitSources = HMIS_Service.Get('LoadXFile', XFile.AdmitSource);
        //$scope.InsuranceContracts = HMIS_Service.Get('InsuranceContract');
        //$scope.XMaritalStatuses = HMIS_Service.Get('LoadXFile', XFile.MaritalStatus);
        //$scope.XGenders = HMIS_Service.Get('LoadXFile', XFile.Gender);
        //$scope.XDiscountTypes = HMIS_Service.Get('LoadXFile', XFile.DiscountType);
        //$scope.Trs = $rootScope.UserTreasuries

        //Departments = HMIS_Service.Get('Department');

        $scope.Reception.Patient = { ID: null, PatientCode: null, Name: null };

        // نوه الزيارة : كشف
        //$scope.Reception.XClinicVisitType = {};
        //$scope.Reception.XClinicVisitType.ID = $scope.XClinicVisitTypes[0].ID;
        $scope.FollowUpPurchase.DateTime = new Date();
        $scope.Reception.DateTime = new Date();
        $scope.FollowUpPurchase.StartDate = new Date();


        $scope.Reception.XVisitPaymentType = {};
        $scope.Reception.XVisitPaymentType.ID = $scope.XVisitPaymentTypes[0].ID;

        $scope.FollowUpPurchase.XFollowUpType = {};
        $scope.FollowUpPurchase.XFollowUpType.ID = $scope.XFollowUpTypes[0].ID;

        $scope.Reception.XVisitType = {};
        $scope.Reception.XVisitType.ID = $scope.XVisitTypes[0].ID;

        var Configurations = HMIS_Service.Get('Configuration');

        $scope.Configuration = { PhoneIsMandatoryInReception: false };

        if (!$rootScope.isNull(Configurations)) {
            $scope.Configuration = Configurations[0];
        }
        $scope.Reception.Patient.Nationality = {};
        $scope.Reception.Patient.Nationality = $scope.Configuration.OrganizationNationality;
    }

    $scope.delete = function (ev) {
        debugger
        var OutpatientReception = {};

        // prepare Reception
        OutpatientReception.Reception = prepare_Reception();

        // prepare FollowUpPurchase
        OutpatientReception.FollowUpPurchase = prepareObject_FollowUpPurchase();

        // prepare OutpatientPayment
        OutpatientReception.OutpatientPayment = prepare_OutpatientPayment();

        HMIS_Service.Delete('OutpatientFollowUpPurchase', OutpatientReception, caller, ev, $mdDialog);
        //HMIS_Service.Delete('FollowUpPurchase', $scope.FollowUpPurchase.ID, caller, ev, $mdDialog);
    }

    $scope.goToList = function () {
        location.replace(caller);
    }



    $scope.PackageName_Autocomplete = function(SearchText)
    {
        if ($scope.FollowUpPurchase.XFollowUpType.ID == 302001)
        {
            //if ($rootScope.isNull($scope.FollowUpPurchase.Clinic) || $rootScope.isNull($scope.FollowUpPurchase.Clinic.ID)) return false;
            //extraCondition = ' AND ClinicID = ' + $scope.FollowUpPurchase.Clinic.ID + '';
            //var data = HMIS_Service.AutoCompelete('Package_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', extraCondition);
        }
        if ($scope.FollowUpPurchase.XFollowUpType.ID == 302002)
        {
            if ($rootScope.isNull($scope.FollowUpPurchase.Clinic) || $rootScope.isNull($scope.FollowUpPurchase.Clinic.ID)) return false;
            extraCondition = ' AND ClinicID = ' + $scope.FollowUpPurchase.Clinic.ID + '';
            var data = HMIS_Service.AutoCompelete('Package_FN()', 'ID, Code, Name,VisitsCount,DaysLimit,MonthsLimit', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', extraCondition);
    }
        return data;
    }

    $scope.Package_select = function (item) {
        $scope.FollowUpPurchase.Package = item;
    }

    $scope.BankName_select = function (item, index) {
        $scope.FollowUpPurchase.TreasuryTransactions[index].Bank = item;
    }

    $scope.EmployeeCode_Autocomplete = function(SearchText)
    {
        var data = HMIS_Service.AutoCompelete('Employee_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', '');
        return data;
    }

    $scope.EmployeeCode_select = function (item, index) {
        $scope.FollowUpPurchase.TreasuryTransactions[index].Employee = item;
    }

    $scope.EmployeeName_Autocomplete = function(SearchText)
    {
        var data = HMIS_Service.AutoCompelete('Employee_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', '');
        return data;
    }

    $scope.EmployeeName_select = function (item, index) {
        $scope.FollowUpPurchase.TreasuryTransactions[index].Employee = item;
    }

    $scope.CommercialPaperCode_Autocomplete = function(SearchText)
    {
        var data = HMIS_Service.AutoCompelete('CommercialPaper_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', '');
        return data;
    }

    $scope.CommercialPaperCode_select = function (item, index) {
        $scope.FollowUpPurchase.TreasuryTransactions[index].CommercialPaper = item;
    }

    $scope.BankAccountCode_Autocomplete = function(SearchText)
    {
        var data = HMIS_Service.AutoCompelete('BankAccount_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', '');
        return data;
    }

    $scope.BankAccountCode_select = function (item, index) {
        $scope.FollowUpPurchase.TreasuryTransactions[index].BankAccount = item;
    }

    $scope.EmplyeeCustodyDocumentCode_Autocomplete = function(SearchText)
    {
        var data = HMIS_Service.AutoCompelete('EmplyeeCustody_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', '');
        return data;
    }

    $scope.EmplyeeCustodyDocumentCode_select = function (item, index) {
        $scope.FollowUpPurchase.TreasuryTransactions[index].EmplyeeCustody = item;
    }



  

    $scope.onInit = function () {

        var Session = $rootScope.userSession();

        $scope.PatientHasOpenedStay = null;

        $scope.XFollowUpTypes = HMIS_Service.Get('LoadXFile', XFile.XFollowUpType);
         $scope.Currencies = HMIS_Service.Get('Currency');
         $scope.XPaymentMethods = HMIS_Service.Get('LoadXFile', XFile.PaymentMethod);
         $scope.Treasuries = HMIS_Service.Get('Treasury');
         $scope.XClinicVisitTypes = HMIS_Service.Get('LoadXFile', XFile.ClinicVisitType);
         //$scope.XVisitPaymentTypes = HMIS_Service.Get('LoadXFile', XFile.VisitPaymentType);
         $scope.XVisitPaymentTypes = HMIS_Service.LoadXFile(XFile.VisitPaymentType, '149001,149002');
         $scope.Cashiers = HMIS_Service.Get('Cashier');
         $scope.XInsuranceMemberTypes = HMIS_Service.Get('LoadXFile', XFile.InsuranceMemberType);
         $scope.PatientClasses = HMIS_Service.Get('PatientClass');
         $scope.XAdmissionTypes = HMIS_Service.Get('LoadXFile', XFile.AdmissionType);
         $scope.XAmbulatoryStatuses = HMIS_Service.Get('LoadXFile', XFile.AmbulatoryStatus);
         $scope.XAdmitSources = HMIS_Service.Get('LoadXFile', XFile.AdmitSource);
         $scope.InsuranceContracts = HMIS_Service.Get('InsuranceContract');
         $scope.XMaritalStatuses = HMIS_Service.Get('LoadXFile', XFile.MaritalStatus);
         $scope.XGenders = HMIS_Service.Get('LoadXFile', XFile.Gender);
         $scope.XVisitTypes = HMIS_Service.LoadXFile(XFile.VisitType, '150001, 150002');
         $scope.Trs = $rootScope.UserTreasuries;

         Departments = HMIS_Service.Get('Department');

         $scope.FollowUpPurchase = {};
         $scope.Reception = {};
         $scope.Reception.minLength = 10;
         $scope.Reception.Patient = { ID: null, PatientCode: null, Name: null };

         $scope.FollowUpPurchase.DateTime = new Date();//.toDateTime();
         $scope.FollowUpPurchase.StartDate = new Date();

         var Configurations = HMIS_Service.Get('Configuration');

         if (!$rootScope.isNull(Configurations)) {
             $scope.Configuration = Configurations[0];
         }

         Departments = HMIS_Service.Get('Department');


         $scope.FollowUpPurchase.XFollowUpType = {};
         $scope.FollowUpPurchase.XFollowUpType.ID = $scope.XFollowUpTypes[0].ID;

         $scope.Reception.XVisitPaymentType = {};
         $scope.Reception.XVisitPaymentType.ID = $scope.XVisitPaymentTypes[0].ID;

         $scope.Reception.XVisitType = {};
         $scope.Reception.XVisitType.ID = $scope.XVisitTypes[0].ID;

         $scope.XDepartmentID = 153008
         selectDepartment($scope.XDepartmentID);

        debugger
         if ($routeParams.id != undefined) {
             $scope.getById($routeParams.id);
         }
    }

    function selectDepartment(XDepartmentID) {

        var y = 0;

        var DepSelected = false;

        while (!DepSelected) {

            if (Departments[y].XDepartmentID == XDepartmentID) {
                $scope.Reception.Department = Departments[y];
                DepSelected = true;
            }

            if (y == Departments.length - 1) DepSelected = true;

            y++;
        }
    }

    //function loadDefaults() {
    //    //// defualt values
    //    $scope.FollowUpPurchase = {};
    //    //$scope.FollowUpPurchase.XVisitType = $scope.XVisitTypes[0];
    //    //$scope.FollowUpPurchase.XVisitPaymentType = $scope.XVisitPaymentTypes[0];
    //}

    $scope.Nationality_Autocomplete = function (SearchText) {
        var data = HMIS_Service.AutoCompelete('Country_FN()', 'ID, Code, Nationality', 'Code, Nationality', '%' + $rootScope.isEmpty(SearchText, '') + '%', '');
        return data;
    }

    $scope.PatientName_Autocomplete = function (SearchText) {
        var extraCondition = ' and  ID not in ';
        extraCondition += '( select PatientID from Stay_FN() where EndDateTime is null )';
        //var data = HMIS_Service.AutoCompelete('Patient_FN()', 'ID, PatientCode, Name, MRN, FirstName, SecondName, ThirdName, ForthName', 'PatientCode, Name, MRN', '%' + SearchText + '%', extraCondition);
        //return data;


        var selectColumns = 'ID, PatientCode, Name, MRN, FirstName, SecondName, ThirdName, ForthName, Mobile';
        selectColumns += ', Address';
        selectColumns += ', NationalID';
        selectColumns += ', Passport';
        selectColumns += ', StayDocumentNum';
        selectColumns += ', XGender_ID, XGender_Name';
        selectColumns += ', XMaritalStatus_ID, XMaritalStatus_Name';

        selectColumns += ', YearOfBirth';
        selectColumns += ', XMonthOfBirth_ID, XMonthOfBirth_Name';
        selectColumns += ', DayOfBirth';
        selectColumns += ', BirthDate';

        selectColumns += ', Nationality_ID, Nationality_Nationality, Nationality_Code , Nationality_Name';
        selectColumns += ', PatientEnglishName';

       
        var data = HMIS_Service.AutoCompelete('X_OutPatient_FN()', selectColumns, 'PatientCode, Name, MRN, Mobile', '%' + $rootScope.isEmpty(SearchText, '') + '%', '');
        return data;
    }

    $scope.Patient_select = function (item) {

        if (item != undefined && item.ID != undefined) {
            $scope.PatientHasOpenedStay = null;

            if (!$rootScope.isNull(item.stay_ID)) {
                $scope.PatientHasOpenedStay = 'المريض ' + item.Name + ' له إقامة مفتوحة '
                //$scope.showSimpleToast('المريض ' + item.Name + ' له إقامة مفتوحة ');

                //msg.alert('هذا المريض له إقامة مفتوحة', $mdDialog);
                item = null;
                $scope.Reception.Patient = null;
                return false;
            }

            $scope.Reception.PatientContact = {};

            $scope.Reception.Patient = item;

            getPatientContact($scope.Reception.Patient.ID);

            // Address
            $scope.Reception.Patient.Address = item.Address;
            // NationalID
            $scope.Reception.Patient.NationalID = item.NationalID;
            $scope.Reception.Patient.PatientEnglishName = item.PatientEnglishName;

            // Passport
            $scope.Reception.Patient.Passport = item.Passport;
            //if ($rootScope.isNull($scope.Reception.PatientContact.Mobile)) {

            //    $scope.Reception.PatientContact.Mobile = item.Mobile;

            //}

            $scope.Reception.Patient.BirthDate = $rootScope.FormatedDate(item.BirthDate);
            // StayDocumentNum
            $scope.Reception.Patient.StayDocumentNum = item.StayDocumentNum;
            // XGender
            $scope.Reception.Patient.XGender = { ID: item.XGender_ID, Name: item.XGender_Name };
            // XMaritalStatus
            $scope.Reception.Patient.XMaritalStatus = { ID: item.XMaritalStatus_ID, Name: item.XMaritalStatus_Name };

            $scope.Reception.Patient.XMonthOfBirth = { ID: item.XMonthOfBirth_ID, Name: item.XMonthOfBirth_Name };

            if (!$rootScope.isNull(item.Country_ID)) {
                $scope.Reception.Patient.Country = { ID: item.Country_ID, Name: item.Country_Name };
            }

            if (!$rootScope.isNull(item.Governor_ID)) {
                $scope.Reception.Patient.Governor = { ID: item.Governor_ID, Name: item.Governor_Name };
            }

            if (!$rootScope.isNull(item.City_ID)) {
                $scope.Reception.Patient.City = { ID: item.City_ID, Name: item.City_Name };
            }

            //if (!$rootScope.isNull($scope.Reception.Patient) && !$rootScope.isNull($scope.Reception.Patient.YearOfBirth)) {
            //    $scope.Reception.Patient.YearOfBirth = { ID: $scope.Reception.Patient.YearOfBirth, Name: $scope.Reception.Patient.YearOfBirth };
            //    $scope.getDays();
            //}

            if (!$rootScope.isNull($scope.Reception.Patient) && !$rootScope.isNull($scope.Reception.Patient.DayOfBirth)) {
                $scope.Reception.Patient.DayOfBirth = { ID: $scope.Reception.Patient.DayOfBirth, Name: $scope.Reception.Patient.DayOfBirth };
            }

            //$scope.Reception.Patient.NationalityID = { ID: item.Nationality_ID, Name: item.Nationality_Nationality };
            if (!$rootScope.isNull($scope.Reception.Patient) && !$rootScope.isNull($scope.Reception.Patient.Nationality_ID)) {

                $scope.Reception.Patient.Nationality = {};
                $scope.Reception.Patient.Nationality.ID = $scope.Reception.Patient.Nationality_ID;
                $scope.Reception.Patient.Nationality.Code = $scope.Reception.Patient.Nationality_Code;
                $scope.Reception.Patient.Nationality.Nationality = $scope.Reception.Patient.Nationality_Nationality;
            }

            $scope.calcAge();

            LastReception = HMIS_Service.Get('LastReception', item.ID);
            if (LastReception != null) {
                $scope.Reception.VisitNo = parseInt(LastReception.VisitNo) + 1;
            }
            else {
                $scope.Reception.VisitNo = 1;
            }
        } else {
            $scope.Reception.Patient = {};
        }

    }

    $scope.newServiceRequest = function () {


        var reception = {};

        reception.ID = $scope.Reception.ID;

        reception.ReservationID = $scope.Requests[0].ReservationID;

        reception.myTime = $scope.ClinicReservation;

        reception.Patient = $scope.Reception.Patient;

        reception.VisitNo = $scope.Reception.VisitNo;

        sessionStorage.setItem('reception', JSON.stringify(reception));

        location.replace('#!ServiceRequestDetail');

    }

    $scope.MainInsuranceCompany_Autocomplete = function (SearchText) {
        var data = HMIS_Service.AutoCompelete('MainInsuranceCompany_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', '');
        return data;
    }

    $scope.MainInsuranceCompany_select = function (item, index) {
        debugger
        if (item != undefined) {
            $scope.Reception.PatientInsuranceCompanies[index].InsuranceCompany = null;
        }
    }

    $scope.InsuranceCompany_Autocomplete = function (row, SearchText) {
        debugger
        var extraConditions = ''
        var extraCondition = ''
        item = $scope.Reception.XVisitPaymentType;
        if (!$rootScope.isNull(item) && item.ID == 149010) {
            extraConditions = extraCondition + ' and ID in (select InsuranceContract.InsuranceCompanyID from InsuranceContract where InsuranceContract.XContractTypeID = 172004) ';
        } else if ((!$rootScope.isNull(row.MainInsuranceCompany)) || ($rootScope.isNull(item) && item.ID != 149010)) {
            extraConditions = ' AND MainInsuranceCompanyID = ' + row.MainInsuranceCompany.ID + '';
        }
        var data = HMIS_Service.AutoCompelete('InsuranceCompany_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', extraConditions);
        return data;
    }

    $scope.Contract_Autocomplete = function (row, SearchText) {
        if ($rootScope.isNull(row) || $rootScope.isNull(row.InsuranceCompany)) return false;
        extraCondition = ' AND InsuranceCompanyID = ' + row.InsuranceCompany.ID + '';
        var data = HMIS_Service.AutoCompelete('InsuranceContract_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', extraCondition);
        return data;
    }

    $scope.Policy_Autocomplete = function (row, SearchText) {
        if ($rootScope.isNull(row) || $rootScope.isNull(row.InsuranceContract)) return false;
        extraCondition = ' AND InsuranceContractID = ' + row.InsuranceContract.ID + '';
        var data = HMIS_Service.AutoCompelete('Policy_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', extraCondition);
        return data;
    }

    $scope.InsuranceCompany_select = function (row) {
        row.InsuranceContract = null;
        row.Policy = null;
    }

    $scope.Contract_select = function (row) {

        row.Policy = null;

        $scope.XContractTypeNotification = null;

        if (!$rootScope.isNull(row)
            && !$rootScope.isNull(row.InsuranceContract)
            && !$rootScope.isNull(row.InsuranceContract.ID)) {
            ContractTypeNotification(row.InsuranceContract.ID);
        }

    }

    function ContractTypeNotification(ContractID) {
        var args = {};
        myXContractTypeID = null;
        args.Name = 'InsuranceContract_ByID';
        args.Params = [{ Name: 'ID', Value: ContractID }];
        var contract = HMIS_Service.GetObject('Generic', args);
        myXContractTypeID = contract.XContractTypeID;
        if (contract.XContractTypeID == 172003) {
            $scope.XContractTypeNotification = ('عقود الملاحة يتم احتساب الخصم على حصة الجهة فقط');
        }
    }

    $scope.SplitPatientName = function () {
        if ($scope.Reception.Patient == undefined) return false;

        $scope.Reception.Patient.FirstName = $scope.Reception.Patient.Name == undefined ? '' : $scope.Reception.Patient.Name.toString().split(' ')[0];
        $scope.Reception.Patient.SecondName = $scope.Reception.Patient.Name == undefined ? '' : $scope.Reception.Patient.Name.toString().split(' ')[1];
        $scope.Reception.Patient.ThirdName = $scope.Reception.Patient.Name == undefined ? '' : $scope.Reception.Patient.Name.toString().split(' ')[2];
        $scope.Reception.Patient.ForthName = $scope.Reception.Patient.Name == undefined ? '' : $scope.Reception.Patient.Name.toString().split(' ')[3];
    }

    $scope.PatientName = function () {
        if ($scope.Reception.Patient == undefined) return false;
        var FirstName = $scope.Reception.Patient.FirstName == undefined ? '' : $scope.Reception.Patient.FirstName.toString();
        var SecondName = $scope.Reception.Patient.SecondName == undefined ? '' : $scope.Reception.Patient.SecondName.toString();
        var ThirdName = $scope.Reception.Patient.ThirdName == undefined ? '' : $scope.Reception.Patient.ThirdName.toString();
        var ForthName = $scope.Reception.Patient.ForthName == undefined ? '' : $scope.Reception.Patient.ForthName.toString();

        $scope.Reception.Patient.Name = FirstName + ' ' + SecondName + ' ' + ThirdName + ' ' + ForthName;
    }
    function collectPatientName() {
        if ($scope.Reception.Patient == undefined) return false;
        var FirstName = $scope.Reception.Patient.FirstName == undefined ? '' : $scope.Reception.Patient.FirstName.toString();
        var SecondName = $scope.Reception.Patient.SecondName == undefined ? '' : $scope.Reception.Patient.SecondName.toString();
        var ThirdName = $scope.Reception.Patient.ThirdName == undefined ? '' : $scope.Reception.Patient.ThirdName.toString();
        var ForthName = $scope.Reception.Patient.ForthName == undefined ? '' : $scope.Reception.Patient.ForthName.toString();

        $scope.Reception.Patient.Name = FirstName + ' ' + SecondName + ' ' + ThirdName + ' ' + ForthName;
    }

    $scope.calcDaysEndDate = function () {
        debugger
        var startDate = new Date($scope.FollowUpPurchase.StartDate);

        if ($scope.FollowUpPurchase.XFollowUpType.ID == 302002) {
              var dayCount = parseFloat($scope.FollowUpPurchase.Package.DaysLimit);
        } else {
              var dayCount = parseFloat($scope.FollowUpPurchase.DayCount);
        }
        if (dayCount) {
        $scope.FollowUpPurchase.EndDate = startDate.setDate(startDate.getDate() + dayCount - 1);
        }
        //$scope.calcMonthsEndDate();
    }

    $scope.calcMonthsEndDate = function () {
        debugger
        var startDate = new Date($rootScope.FormatedDate($scope.FollowUpPurchase.StartDate));

        if ($scope.FollowUpPurchase.XFollowUpType.ID == 302002) {
            var monthCount = parseFloat($scope.FollowUpPurchase.Package.MonthsLimit);
        } else {
            var monthCount = parseFloat($scope.FollowUpPurchase.MonthCount);
        }
        if (monthCount) {
        $scope.FollowUpPurchase.EndDate = startDate.setMonth(startDate.getMonth() + monthCount);
        }
        //$scope.calcDaysEndDate();
    }

    $scope.calcEndDate = function () {
        debugger
        var monthCount = $scope.FollowUpPurchase.MonthCount | 0;
        var dayCount = $scope.FollowUpPurchase.DayCount | 0;

        var curYear = new Date().getFullYear();

        var curMonths = new Date($scope.FollowUpPurchase.StartDate).getMonth();
        var curDays = new Date($scope.FollowUpPurchase.StartDate).getDate();
        //var curMonths = new Date().getMonth();
        //var curDays = new Date().getDate();

        var y = curYear;
        var m = curMonths + monthCount;
        var d = curDays + dayCount - 1;

        var EndDate = new Date(y, m, d, 0, 0, 0, 0);

        $scope.FollowUpPurchase.EndDate = EndDate;
    }

    $scope.len = function (obj) {
        if ($rootScope.isNull(obj)) {
            return 0;
        }
        else {
            return obj.toString().length;
        }
    }

    $(function () {
        var yearNOW = new Date().getFullYear();
        var range = [];
        var year = 1900
        for (var i = 1; i < yearNOW - year + 1; i++) {
            range.push({ ID: year + i, Name: year + i });
        }
        $scope.years = range;
    });

    $scope.getDays = function () {

        var lastDay;
        $scope.DayOfBirth = [];

        if ($scope.Reception.Patient.YearOfBirth != undefined && $scope.Reception.Patient.XMonthOfBirth != undefined) {
            if ($scope.Reception.Patient.XMonthOfBirth.Code == '2') {
                if (($scope.Reception.Patient.YearOfBirth % 4) == 0) {
                    lastDay = 29;
                }
                else {
                    lastDay = 28;
                }
            }
            else {
                if ($scope.Reception.Patient.XMonthOfBirth.Code == '4' ||
                    $scope.Reception.Patient.XMonthOfBirth.Code == '6' ||
                    $scope.Reception.Patient.XMonthOfBirth.Code == '9' ||
                    $scope.Reception.Patient.XMonthOfBirth.Code == '11') {
                    lastDay = 30;
                }
                else {
                    lastDay = 31;
                }
            }

            $scope.maxDay = lastDay;

            for (var i = 1; i <= lastDay; i++) {
                var day = { ID: i, Name: i };
                $scope.DayOfBirth.push(day);
            }
        }
    }

    $scope.calcAge = function () {

        var d1 = new Date($scope.Reception.Patient.BirthDate);

        var d2 = new Date();

        var diff = d2.getTime() - d1.getTime();

        var diffInYears = (diff / (1000 * 60 * 60 * 24 * 365.25));
        var AgeInYears = Math.floor(diffInYears);

        var NumberAfterYears = parseFloat(diffInYears) - parseInt(diffInYears);

        var DiffInMonths = NumberAfterYears * 12;
        var AgeInMonths = Math.floor(DiffInMonths);

        var NumberAfterMonths = parseFloat(DiffInMonths) - parseInt(DiffInMonths);

        var AgeInDays = NumberAfterMonths * 30;
        AgeInDays = Math.floor(AgeInDays);

        $scope.Reception.Patient.AgeYears = AgeInYears;
        $scope.Reception.Patient.AgeMonths = AgeInMonths;
        $scope.Reception.Patient.AgeDays = AgeInDays;

    }


    $scope.calcBirthDate = function () {

        var AgeYears = $scope.Reception.Patient.AgeYears | 0;
        var AgeMonths = $scope.Reception.Patient.AgeMonths | 0;
        var AgeDays = $scope.Reception.Patient.AgeDays | 0;

        var curYear = new Date().getFullYear();
        var curMonths = new Date().getMonth();
        ;
        var curDays = new Date().getDate();

        var y = curYear - AgeYears;
        var m = curMonths - AgeMonths;
        var d = curDays - AgeDays;

        var birthDate = new Date(y, m, d, 0, 0, 0, 0);

        // تاريخ الميلاد
        $scope.Reception.Patient.BirthDate = birthDate;

    }

    $scope.TreasuryName_Autocomplete = function (SearchText) {
        var data = HMIS_Service.AutoCompelete('Treasury_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', '');
        return data;
    }

    // Add --> Reception_PatientInsuranceCompany
    $scope.add_PatientInsuranceCompany = function () {

        if ($scope.Reception == null) {
            $scope.Reception = {};
        }

        if ($scope.Reception.PatientInsuranceCompanies == null) {
            $scope.Reception.PatientInsuranceCompanies = [];
        }

        var newItem = {};
        newItem.Status = 1;
        newItem.index = $scope.Reception.PatientInsuranceCompanies.length + 1;
        $scope.Reception.PatientInsuranceCompanies.push(newItem);

        getPatientInsuranceCompaniesCount();
    }
    // Remove --> Reception_PatientInsuranceCompany
    $scope.remove_PatientInsuranceCompany = function (item) {
        if (item.ID != null) {
            item.Status = 3;
        }
        else {
            var index = $scope.Reception.PatientInsuranceCompanies.indexOf(item);
            $scope.Reception.PatientInsuranceCompanies.splice(index, 1);
        }

        getPatientInsuranceCompaniesCount();
    }

    function getPatientInsuranceCompaniesCount() {
        var count = 0;
        angular.forEach($scope.Reception.PatientInsuranceCompanies, function (item, i) {
            if (item.Status != 3) {
                count += 1;
            }
        });

        $scope.Reception.PatientInsuranceCompaniesCount = count;
    }

    $scope.XFollowUpType_changed = function () {
        debugger
        //item = $scope.FollowUpPurchase.XFollowUpType;
        if ($scope.FollowUpPurchase.XFollowUpType.ID == 302001) {
            $scope.FollowUpPurchase.Package = null;

            //$scope.FollowUpPurchase.Package.VisitsCount = null;
            //$scope.FollowUpPurchase.Package.DaysLimit   = null;
            //$scope.FollowUpPurchase.Package.MonthsLimit = null;
            //$scope.FollowUpPurchase.EndDate = null;
        }
        if ($scope.FollowUpPurchase.XFollowUpType.ID == 302002) {
            debugger
            $scope.FollowUpPurchase.VisitsCount = null;
            $scope.FollowUpPurchase.DayCount = null;
            $scope.FollowUpPurchase.MonthCount = null;
            $scope.FollowUpPurchase.EndDate = null;
            $scope.FollowUpPurchase.Package = null;
            $scope.FollowUpPurchase.FollowUpPurchaseService = [];
        }
    }

    $scope.XVisitPaymentType_changed = function () {

        item = $scope.Reception.XVisitPaymentType;

        if (!$rootScope.isNull(item) && ($rootScope.isNull($scope.Reception.PatientInsuranceCompanies) || $scope.Reception.PatientInsuranceCompanies.length == 0) && ($rootScope.toNumber(item.ID) == 149002 || $rootScope.toNumber(item.ID) == 149010)) {
            $scope.add_PatientInsuranceCompany();
        }
        if (!$rootScope.isNull(item) && (!$rootScope.isNull($scope.Reception.PatientInsuranceCompanies)) && ($rootScope.toNumber(item.ID) == 149001)) {
            $scope.Reception.PatientInsuranceCompanies = [];
            getPatientInsuranceCompaniesCount();
            //$scope.remove_PatientInsuranceCompany($scope.Reception.PatientInsuranceCompanies[0]);
        }

        if (!$rootScope.isNull(item)) {
            $scope.InsuranceCase = ($rootScope.toNumber(item.ID) == XVisitPaymentType.Insurance || $rootScope.toNumber(item.ID) == 149010)
        }

        //reviewServices();

    }

    // OutpatientPayment
    $scope.newOutpatientPayment = function () {
        $scope.OutpatientPayment = {};
        // add new Treasury Transaction
        $scope.add_TreasuryTransaction();
    }

    $scope.BankName_Autocomplete = function (SearchText) {
        var data = HMIS_Service.AutoCompelete('Bank_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', '');
        return data;
    }

    $scope.BankName_select = function (item) {
        $scope.FollowUpPurchase.Bank = item;
    }

    $scope.getOutpatientPaymentById = function (ID) {
        debugger
        $scope.OutpatientPayment = HMIS_Service.Get('OutpatientPayment', ID);
        var Treasury = $scope.OutpatientPayment.Treasury;
        $scope.OutpatientPayment.Treasury = { ID: Treasury.ID, Code: Treasury.Code, Name: Treasury.Name };
        console.info('Treasury', $scope.OutpatientPayment.Treasury);

        //if ($rootScope.isNull($scope.OutpatientPayment.TreasuryTransactions)) {
        //    $scope.OutpatientPayment.Treasury = $scope.OutpatientPayment.Treasury;
        //}

        getTreasuryTransactionsCount();

    }

    $scope.ServiceRequestDocumentCode_Autocomplete = function (SearchText) {
        var extraCondition = 'order by id desc';
        var data = HMIS_Service.AutoCompelete('ServiceRequest_FN()', 'ID, DocumentCode, Patient_Name', 'ID, DocumentCode, Patient_Name', '%' + SearchText + '%', extraCondition);
        return data;
    }

    $scope.ServiceRequestDocumentCode_select = function (item) {

        $scope.OutpatientPayment.ServiceRequest = item;

        if (item != undefined) {

            var args = {};
            args.Name = 'X_OutPatientPaymentServiceRequestID_TotalRequested';
            args.Params = [{ Name: 'ServiceRequestID', Value: item.ID }];
            data = HMIS_Service.Post('Generic', args);
            if (data != undefined && data.length > 0) {
                data = data[0];
                $scope.OutpatientPayment.TotalRequested = data.TotalRequested;
            }
        }
        if (item != undefined) {

            var args = {};
            args.Name = 'X_OutPatientPayment_PreviousPayment';
            args.Params = [{ Name: 'ServiceRequestID', Value: item.ID }];
            data = HMIS_Service.Post('Generic', args);
            if (data != undefined && data.length > 0) {
                data = data[0];
                $scope.OutpatientPayment.PreviousPayment = data.PreviousPayment;
            }

            //data = HMIS_Service.Get('PreviousPayment', item.ID);
            //data = JSON.parse(data)[0];
            //$scope.OutpatientPayment.PreviousPayment = data.PreviousPayment;
        }

    }

    $scope.getTotal = function () {
        $scope.OutpatientPayment.Paid = 0;
        $($scope.OutpatientPayment.TreasuryTransactions).each(function (i, item) {
            if (item.Status != 3) {
                $scope.OutpatientPayment.Paid += toNumber(item.Value == null ? 0 : item.Value);
            }
        });
        $scope.OutpatientPayment.Paid = toNumber($scope.OutpatientPayment.Paid).toFixed(2);
    }

    function isEmpty(OutpatientPayment) {

        if (OutpatientPayment == undefined) {
            return true;
        }

        if (OutpatientPayment == null) {
            return true;
        }

        if (OutpatientPayment == '') {
            return true;
        }

        return false;

    }

    function toNumber($this) {

        var myValue = $this;

        myValue = isEmpty($this) ? 0 : parseFloat(myValue);

        return myValue;

    }


    $scope.XServiceTypeName_Autocomplete = function (SearchText) {
        var data = HMIS_Service.AutoCompelete('XServiceType_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', '');
        return data;
    }

    $scope.XServiceTypeName_select = function (item, index) {
        $scope.FollowUpPurchase.FollowUpPurchaseServices[index].XServiceType = item;
    }

    $scope.ServiceName_Autocomplete = function (SearchText,r) {
        if ($rootScope.isNull(r) || $rootScope.isNull(r.XServiceType.ID)) return false;
        extraCondition = ' AND XServiceTypeID = ' + r.XServiceType.ID + '';
        var data = HMIS_Service.AutoCompelete('Service_FN()', 'ID, Code, Name', 'Code, Name', '%' + $rootScope.isEmpty(SearchText, '') + '%', extraCondition);
        return data;
    }

    $scope.ServiceName_select = function (item, index) {
        $scope.FollowUpPurchase.FollowUpPurchaseServices[index].Service = item;
    }

    $scope.getPackageName_select = function (item) {
        debugger
        $scope.FollowUpPurchase.Package = item;
        $scope.calcEndDate();

        //if (item != null) {
        //    var Package = HMIS_Service.Get('Package', item.ID);

        //    if (Package.PackageClinicServices != null && Package.PackageClinicServices.length > 0) {
        //        $scope.FollowUpPurchase.FollowUpPurchaseServices = [];
        //        $(Package.PackageClinicServices).each(function (i, item) {
        //            item.Status = 1;
        //            args = {};
        //            args.Name = 'X_GetXServiceType';
        //            args.Params = [
        //                { Name: 'ServiceID', Value: item.Service.ID }
        //            ];
        //            XServiceType = HMIS_Service.Post('Generic', args);
        //            if (XServiceType != undefined) {
        //                item.XServiceType = { ID: XServiceType[0].ID, Code: XServiceType[0].Code, Name: XServiceType[0].Name };
        //            }
        //            item.ServiceCount = item.Count;
        //            $scope.FollowUpPurchase.FollowUpPurchaseServices.push(item);
        //        });
        //    }
        //}
    }

    $scope.PackageName_select = function (item) {

        $scope.FollowUpPurchase.Package = item;
        if (item != null) {
            $scope.calcEndDate();
            var Package = HMIS_Service.Get('Package', item.ID);
            
            $scope.FollowUpPurchase.VisitsCount = $scope.FollowUpPurchase.Package.VisitsCount;
            $scope.FollowUpPurchase.DayCount = $scope.FollowUpPurchase.Package.DaysLimit;
            $scope.FollowUpPurchase.MonthCount = $scope.FollowUpPurchase.Package.MonthsLimit;
            //$scope.FollowUpPurchase.EndDate = $scope.FollowUpPurchase.Package.EndDate;

            if (Package.PackageClinicServices != null && Package.PackageClinicServices.length > 0) {
                $scope.FollowUpPurchase.FollowUpPurchaseServices = [];
                $(Package.PackageClinicServices).each(function (i, item) {
                    item.Status = 1;
                    args = {};
                    args.Name = 'X_GetXServiceType';
                    args.Params = [
                        { Name: 'ServiceID', Value: item.Service.ID }
                    ];
                    XServiceType = HMIS_Service.Post('Generic', args);
                    if (XServiceType != undefined) {
                        item.XServiceType = { ID: XServiceType[0].ID, Code: XServiceType[0].Code, Name: XServiceType[0].Name };
                    }debugger
                    item.ServiceCount = item.Count;
                    item.Price = item.Cost;
                    $scope.FollowUpPurchase.FollowUpPurchaseServices.push(item);
                });
            }
            debugger
            args = {};
            args.Name = 'X_GetPackagePrice_FollowUp';
            args.Params = [
                { Name: 'PackageID', Value: item.ID }
            ];
            PackagePrice = HMIS_Service.Post('Generic', args);
            if (PackagePrice != undefined) {
                var x = Object.values(PackagePrice[0]);
                $scope.FollowUpPurchase.Price = (x[0]);
                $scope.FollowUpPurchase.PatientValue = $scope.FollowUpPurchase.Price;
            }
        } else {
            //$scope.FollowUpPurchase.FollowUpPurchaseServices = [];
        }

    }

    $scope.CalcTotalPaid = function () {
        debugger
        $scope.FollowUpPurchase.TotalAdvance = (+($rootScope.toNumber($scope.FollowUpPurchase.CashPaid)) + +($rootScope.toNumber($scope.FollowUpPurchase.VisaPaid))).toFixed(2);

    }

    $scope.NetPatientDiscount = function (ind) {

        debugger
        $scope.FollowUpPurchase.PatientValueAfterDiscount = 0;
        $scope.FollowUpPurchase.PatientValue = $scope.FollowUpPurchase.Price;

        if (ind == 1) {// قيمة
            var PatientValue = 0;
            if (!$rootScope.isNull($scope.FollowUpPurchase.PatientValue)) {
                PatientValue = $scope.FollowUpPurchase.PatientValue;
            }

            var Discount = 0;
            if (!$rootScope.isNull($scope.FollowUpPurchase.PatientDiscount)) {
                Discount = $scope.FollowUpPurchase.PatientDiscount;
            }

            // نسبة الخصم
            var DiscountPercent = Discount / PatientValue * 100;
            DiscountPercent = parseFloat(DiscountPercent).toFixed(2);
            DiscountPercent = parseFloat(DiscountPercent);
            $scope.FollowUpPurchase.PatientDiscountPercent = DiscountPercent;

            var valAfterDisc = 0;
            valAfterDisc = PatientValue - Discount;
            valAfterDisc = parseFloat(valAfterDisc).toFixed(2);
            valAfterDisc = parseFloat(valAfterDisc);

            $scope.FollowUpPurchase.PatientValueAfterDiscount = valAfterDisc;
            //if (Reception.XVisitPaymentType.ID == 149001) {
            //$scope.FollowUpPurchase.PatientValue = valAfterDisc;
            //}

        }

        if (ind == 2) {// نسبة
            var PatientValue = 0;
            if (!$rootScope.isNull($scope.FollowUpPurchase.PatientValue)) {
                PatientValue = $scope.FollowUpPurchase.PatientValue;
            }

            var DiscountPercent = 0;
            if (!$rootScope.isNull($scope.FollowUpPurchase.PatientDiscountPercent)) {
                DiscountPercent = $scope.FollowUpPurchase.PatientDiscountPercent / 100;
            }

            var Discount = DiscountPercent * PatientValue;
            Discount = parseFloat(Discount).toFixed(2);
            Discount = parseFloat(Discount);
            $scope.FollowUpPurchase.PatientDiscount = Discount;

            var valAfterDisc = 0;
            valAfterDisc = PatientValue - Discount;
            valAfterDisc = parseFloat(valAfterDisc).toFixed(2);
            valAfterDisc = parseFloat(valAfterDisc);

            $scope.FollowUpPurchase.PatientValueAfterDiscount = valAfterDisc;
            //if (Reception.XVisitPaymentType.ID == 149001) {
            //    $scope.FollowUpPurchase.PatientValue = valAfterDisc;
            //}
        }

        // $scope.calcTotalInvoice();

        //   $scope.TotalsCalculation();

    }

    $scope.NetOrgValue = function (ind) {

        debugger
        $scope.FollowUpPurchase.OrgValue = 0;

        if (ind == 1) {// قيمة
            var Price = 0;
            if (!$rootScope.isNull($scope.FollowUpPurchase.Price)) {
                Price = $scope.FollowUpPurchase.Price;
            }

            var Discount = 0;
            if (!$rootScope.isNull($scope.FollowUpPurchase.OrgDiscountValue)) {
                Discount = $scope.FollowUpPurchase.OrgDiscountValue;
            }

            // نسبة الخصم
            var DiscountPercent = Discount / Price * 100;
            DiscountPercent = parseFloat(DiscountPercent).toFixed(2);
            DiscountPercent = parseFloat(DiscountPercent);
            $scope.FollowUpPurchase.OrgDiscountPercent = DiscountPercent;

            var valAfteOrgValuerDisc = 0;
            OrgValue = Price - Discount;
            OrgValue = parseFloat(OrgValue).toFixed(2);
            OrgValue = parseFloat(OrgValue);

            $scope.FollowUpPurchase.OrgDiscountPrice = OrgValue;

        }

        if (ind == 2) {// نسبة
            var Price = 0;
            if (!$rootScope.isNull($scope.FollowUpPurchase.Price)) {
                Price = $scope.FollowUpPurchase.Price;
            }

            var DiscountPercent = 0;
            if (!$rootScope.isNull($scope.FollowUpPurchase.OrgDiscountPercent)) {
                DiscountPercent = $scope.FollowUpPurchase.OrgDiscountPercent / 100;
            }

            var Discount = DiscountPercent * Price;
            Discount = parseFloat(Discount).toFixed(2);
            Discount = parseFloat(Discount);
            $scope.FollowUpPurchase.OrgDiscountValue = Discount;

            var OrgValue = 0;
            OrgValue = Price - Discount;
            OrgValue = parseFloat(OrgValue).toFixed(2);
            OrgValue = parseFloat(OrgValue);

            $scope.FollowUpPurchase.OrgDiscountPrice = OrgValue;
           
        }

        // $scope.calcTotalInvoice();

        //   $scope.TotalsCalculation();

    }

    $scope.NetPatientandOrgPercent = function (ind) {

        debugger

        if (ind == 1) {
            //var PatientPercent = 0;
            if (!$rootScope.isNull($scope.FollowUpPurchase.PatientPercent)) {
                $scope.FollowUpPurchase.OrgPercent = 100 - $scope.FollowUpPurchase.PatientPercent;
            }
        }
        if (ind == 2) {
            //var OrgPercent = 0;
            if (!$rootScope.isNull($scope.FollowUpPurchase.OrgPercent)) {
                $scope.FollowUpPurchase.PatientPercent = 100 - $scope.FollowUpPurchase.OrgPercent;
            }
        }
    }

    $scope.CalcPatientandOrgPercent = function () {
        debugger
        //$scope.FollowUpPurchase.OrgValue = parseFloat($scope.FollowUpPurchase.OrgDiscountPrice) * ($scope.FollowUpPurchase.OrgPercent / 100).toFixed(2);
        //$scope.FollowUpPurchase.PatientValue = parseFloat($scope.FollowUpPurchase.OrgDiscountPrice) * ($scope.FollowUpPurchase.PatientPercent / 100).toFixed(2);


        var OrgValue = 0;
        OrgValue = $scope.FollowUpPurchase.OrgDiscountPrice * $scope.FollowUpPurchase.OrgPercent / 100;
        OrgValue = parseFloat(OrgValue).toFixed(2);
        OrgValue = parseFloat(OrgValue);
        $scope.FollowUpPurchase.OrgValue = OrgValue;

        var PatientValue = 0;
        PatientValue = $scope.FollowUpPurchase.OrgDiscountPrice * $scope.FollowUpPurchase.PatientPercent / 100;
        PatientValue = parseFloat(PatientValue).toFixed(2);
        PatientValue = parseFloat(PatientValue);
        $scope.FollowUpPurchase.PatientValue = PatientValue;
    }

    // Add --> OutpatientPayment_TreasuryTransaction
    $scope.add_TreasuryTransaction = function () {

        if ($scope.OutpatientPayment == null) {
            $scope.OutpatientPayment = {};
        }

        if ($scope.OutpatientPayment.TreasuryTransactions == null) {
            $scope.OutpatientPayment.TreasuryTransactions = [];
        }

        var newItem = {};
        newItem.Status = 1;
        newItem.index = $scope.OutpatientPayment.TreasuryTransactions.length + 1;

        newItem.Currency = $scope.Currencies[0];
        //newItem.XPaymentMethod = $scope.XPaymentMethods[0];

        $scope.OutpatientPayment.TreasuryTransactions.push(newItem);

        getTreasuryTransactionsCount();
    }
    // Remove --> OutpatientPayment_TreasuryTransaction
    $scope.remove_TreasuryTransaction = function (item) {
        if (item.ID != null) {
            item.Status = 3;
        }
        else {
            var index = $scope.OutpatientPayment.TreasuryTransactions.indexOf(item);
            $scope.OutpatientPayment.TreasuryTransactions.splice(index, 1);
        }

        getTreasuryTransactionsCount();
        ;
        $scope.getTotal();
    }

    function getTreasuryTransactionsCount() {
        var count = 0;
        if ($rootScope.isNull($scope.OutpatientPayment) || $rootScope.isNull($scope.OutpatientPayment.TreasuryTransactions)) return false;
        angular.forEach($scope.OutpatientPayment.TreasuryTransactions, function (item, i) {
            if (item.Status != 3) {
                count += 1;
            }
        });

        $scope.OutpatientPayment.TreasuryTransactionsCount = count;
    }
    // FollowUpPurchaseService
    // Add --> FollowUpPurchase_FollowUpPurchaseService
    $scope.add_FollowUpPurchaseService = function () {

        if ($scope.FollowUpPurchase == null) {
            $scope.FollowUpPurchase = {};
        }

        if ($scope.FollowUpPurchase.FollowUpPurchaseServices == null) {
            $scope.FollowUpPurchase.FollowUpPurchaseServices = [];
        }

        var newItem = {};
        newItem.Status = 1;
        newItem.index = $scope.FollowUpPurchase.FollowUpPurchaseServices.length + 1;
        $scope.FollowUpPurchase.FollowUpPurchaseServices.push(newItem);

        getFollowUpPurchaseServicesCount();
    }
    // Remove --> FollowUpPurchase_FollowUpPurchaseService
    $scope.remove_FollowUpPurchaseService = function (item) {
        if (item.ID != null) {
            item.Status = 3;
        }
        else {
            var index = $scope.FollowUpPurchase.FollowUpPurchaseServices.indexOf(item);
            $scope.FollowUpPurchase.FollowUpPurchaseServices.splice(index, 1);
        }

        getFollowUpPurchaseServicesCount();
    }

    function getFollowUpPurchaseServicesCount() {
        var count = 0;
        angular.forEach($scope.FollowUpPurchase.FollowUpPurchaseServices, function (item, i) {
            if (item.Status != 3) {
                count += 1;
            }
        });

        $scope.FollowUpPurchase.FollowUpPurchaseServicesCount = count;
    }
    $scope.checkServiceUniqueness = function (services, row, index, uniquenessName) {

        uniquenessName = angular.isUndefined(uniquenessName) ? 'Service' : uniquenessName;

        if (!$rootScope.isNull(services) && services.length > 1) {

            angular.forEach(services, function (item, i) {

                // الكشف
                if (
                    (!$rootScope.isNull(item[uniquenessName]) && !$rootScope.isNull(row[uniquenessName]))
                    &&
                    (!$rootScope.isNull(item[uniquenessName].ID) && !$rootScope.isNull(row[uniquenessName].ID))
                    &&
                    (item[uniquenessName].ID == row[uniquenessName].ID && index != i)) {

                    var message = row[uniquenessName].Name + ' موجود من قبل في الصف رقم ' + parseInt(i + 1);

                    msg.alert(message); //, $mdDialog)

                    row[uniquenessName] = null;
                }

            });

        }

    }

    $scope.print = function () {
        debugger
        //if ($rootScope.config.ClientName.toLowerCase() == "Aiadty".toLowerCase()) {
        $scope.print_OfficalReceipt = true;
        var OutpatientPaymentID = $scope.Payments[0].ID;
        var PatientNameEnglishInd;
        $scope.Reception.PrintEnglishName;
        if ($scope.Reception.PrintEnglishName == true) {
            PatientNameEnglishInd = 1;
        } else {
            PatientNameEnglishInd = 0;
        }
        var data = {
            LogoPath: $rootScope.config.reports.OutpatientPaymentLogoPath,
            ID: OutpatientPaymentID,
            ReportTitle: 'إيصال سداد رقم ',
            Ind: parseInt($scope.XDepartmentID),
            ReceiptIndicator: $rootScope.config.reports.OutpatientPaymentReportLayout,
            UserName: $rootScope.PersonName,
            PatientNameEnglishInd: PatientNameEnglishInd,
            PrintOfficalReceiptInd: $scope.print_OfficalReceipt
        };
        HMIS_Service.PrintAsyn('OutpatientPaymentAiadtyNEW_Rep', data, $scope);
        //}
    }

    $scope.onInit();

});
