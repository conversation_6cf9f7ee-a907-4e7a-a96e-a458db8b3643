.mapping-card, .placeholder-card {
  margin: 20px;
  max-width: 1400px;
}

.mapping-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.mapping-summary {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
}

.summary-item mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.mapping-table {
  width: 100%;
}

.mapping-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.mapping-table td {
  padding: 16px 8px;
  vertical-align: top;
}

.parameter-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.parameter-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.parameter-details mat-chip-set {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.mapping-type-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mapping-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mapping-option mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.mapping-field {
  width: 100%;
  min-width: 200px;
}

.not-mapped-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-style: italic;
}

.not-mapped-message mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.preview-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-container mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.preview-text {
  font-size: 14px;
  color: #666;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
  text-align: center;
  color: #666;
}

.placeholder-content mat-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  color: #ccc;
}

.placeholder-content h3 {
  margin: 16px 0 8px 0;
  color: #333;
}

.placeholder-content p {
  margin: 4px 0;
  max-width: 400px;
}

/* Responsive design */
@media (max-width: 1200px) {
  .mapping-table {
    font-size: 14px;
  }
  
  .mapping-table td, .mapping-table th {
    padding: 12px 6px;
  }
}

@media (max-width: 768px) {
  .mapping-actions {
    flex-direction: column;
  }
  
  .mapping-actions button {
    width: 100%;
  }
  
  .mapping-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .mapping-table {
    font-size: 12px;
  }
  
  .mapping-table td, .mapping-table th {
    padding: 8px 4px;
  }
  
  .mapping-type-group {
    gap: 4px;
  }
  
  .mapping-field {
    min-width: 150px;
  }
  
  .preview-text {
    max-width: 100px;
  }
  
  .parameter-details mat-chip-set {
    gap: 2px;
  }
}

/* Radio button styling */
::ng-deep .mapping-option .mat-radio-label {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

::ng-deep .mapping-option .mat-radio-label-content {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}
