<mat-card class="search-card">
  <mat-card-header>
    <mat-card-title>Search Stored Procedures</mat-card-title>
  </mat-card-header>
  
  <mat-card-content>
    <!-- Search Input -->
    <div class="search-container">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search procedures...</mat-label>
        <input 
          matInput 
          [(ngModel)]="searchTerm" 
          (keypress)="onKeyPress($event)"
          placeholder="Enter procedure name or description">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
      
      <button 
        mat-raised-button 
        color="primary" 
        (click)="onSearch()"
        [disabled]="!searchTerm.trim() || isLoading"
        class="search-button">
        <mat-icon>search</mat-icon>
        Search
      </button>
      
      <button 
        mat-stroked-button 
        (click)="clearSearch()"
        [disabled]="!hasSearched"
        class="clear-button">
        <mat-icon>clear</mat-icon>
        Clear
      </button>
    </div>

    <!-- Loading Spinner -->
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Searching procedures...</p>
    </div>

    <!-- Results Table -->
    <div *ngIf="!isLoading && hasSearched" class="results-container">
      <h3>Search Results ({{procedures.length}} found)</h3>
      
      <div *ngIf="procedures.length === 0" class="no-results">
        <mat-icon>info</mat-icon>
        <p>No stored procedures found matching your search criteria.</p>
      </div>

      <table 
        mat-table 
        [dataSource]="procedures" 
        *ngIf="procedures.length > 0"
        class="procedures-table">
        
        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Procedure Name</th>
          <td mat-cell *matCellDef="let procedure">
            <strong>{{procedure.name}}</strong>
          </td>
        </ng-container>

        <!-- Schema Column -->
        <ng-container matColumnDef="schema">
          <th mat-header-cell *matHeaderCellDef>Schema</th>
          <td mat-cell *matCellDef="let procedure">{{procedure.schema}}</td>
        </ng-container>

        <!-- Description Column -->
        <ng-container matColumnDef="description">
          <th mat-header-cell *matHeaderCellDef>Description</th>
          <td mat-cell *matCellDef="let procedure">
            {{procedure.description || 'No description available'}}
          </td>
        </ng-container>

        <!-- Modified Column -->
        <ng-container matColumnDef="modified">
          <th mat-header-cell *matHeaderCellDef>Last Modified</th>
          <td mat-cell *matCellDef="let procedure">
            {{procedure.modified | date:'short'}}
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let procedure">
            <button 
              mat-raised-button 
              color="accent"
              (click)="onSelectProcedure(procedure)"
              [class.selected]="selectedProcedure?.id === procedure.id">
              <mat-icon>check_circle</mat-icon>
              {{selectedProcedure?.id === procedure.id ? 'Selected' : 'Select'}}
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr 
          mat-row 
          *matRowDef="let row; columns: displayedColumns;"
          [class.selected-row]="selectedProcedure?.id === row.id">
        </tr>
      </table>
    </div>

    <!-- Selected Procedure Info -->
    <div *ngIf="selectedProcedure" class="selected-info">
      <mat-icon>check_circle</mat-icon>
      <span>Selected: <strong>{{selectedProcedure.fullName}}</strong></span>
    </div>
  </mat-card-content>
</mat-card>
