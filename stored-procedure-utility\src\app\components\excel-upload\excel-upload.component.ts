import { Component, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ExcelData } from '../../models/stored-procedure.model';
import { ExcelService } from '../../services/excel.service';

@Component({
  selector: 'app-excel-upload',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatTableModule,
    MatChipsModule,
    MatSnackBarModule
  ],
  templateUrl: './excel-upload.component.html',
  styleUrls: ['./excel-upload.component.css']
})
export class ExcelUploadComponent {
  @Output() excelDataLoaded = new EventEmitter<ExcelData>();

  excelData: ExcelData | null = null;
  isLoading: boolean = false;
  isDragOver: boolean = false;

  displayedColumns: string[] = ['name', 'dataType', 'sampleValues'];

  constructor(
    private excelService: ExcelService,
    private snackBar: MatSnackBar
  ) {}

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.processFile(input.files[0]);
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    this.isDragOver = false;
    
    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
      this.processFile(event.dataTransfer.files[0]);
    }
  }

  private processFile(file: File): void {
    // Validate file
    const validation = this.excelService.validateExcelFile(file);
    if (!validation.isValid) {
      this.snackBar.open(validation.error!, 'Close', {
        duration: 5000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    this.isLoading = true;
    this.excelData = null;

    this.excelService.parseExcelFile(file).subscribe({
      next: (data) => {
        this.excelData = data;
        this.isLoading = false;
        this.excelDataLoaded.emit(data);
        this.snackBar.open(
          `Excel file loaded successfully! ${data.totalRows} rows found.`, 
          'Close', 
          { duration: 3000 }
        );
      },
      error: (error) => {
        console.error('Error parsing Excel file:', error);
        this.isLoading = false;
        this.snackBar.open(
          'Error parsing Excel file. Please check the file format.', 
          'Close', 
          { 
            duration: 5000,
            panelClass: ['error-snackbar']
          }
        );
      }
    });
  }

  clearFile(): void {
    this.excelData = null;
    this.excelDataLoaded.emit(null as any);
    
    // Clear file input
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  triggerFileInput(): void {
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    fileInput?.click();
  }

  getSampleValuesDisplay(sampleValues: string[]): string {
    if (sampleValues.length === 0) {
      return 'No data';
    }
    
    const maxDisplay = 3;
    const displayed = sampleValues.slice(0, maxDisplay);
    const remaining = sampleValues.length - maxDisplay;
    
    let result = displayed.join(', ');
    if (remaining > 0) {
      result += `, +${remaining} more`;
    }
    
    return result;
  }

  getDataTypeColor(dataType: string): string {
    switch (dataType) {
      case 'number': return 'primary';
      case 'date': return 'accent';
      case 'boolean': return 'warn';
      default: return '';
    }
  }
}
