{"version": 3, "sources": ["../../../../../node_modules/@angular/material/fesm2022/radio.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { forwardRef, InjectionToken, EventEmitter, Directive, Output, ContentChildren, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Attribute, ViewChild, NgModule } from '@angular/core';\nimport * as i3 from '@angular/material/core';\nimport { mixinDisableRipple, mixinTabIndex, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i2 from '@angular/cdk/collections';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\n\n// Increasing integer for generating unique ids for radio components.\nconst _c0 = [\"input\"];\nconst _c1 = [\"*\"];\nlet nextUniqueId = 0;\n/** Change event object emitted by radio button and radio group. */\nclass MatRadioChange {\n  constructor(/** The radio button that emits the change event. */\n  source, /** The value of the radio button. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * Provider Expression that allows mat-radio-group to register as a ControlValueAccessor. This\n * allows it to support [(ngModel)] and ngControl.\n * @docs-private\n */\nconst MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatRadioGroup),\n  multi: true\n};\n/**\n * Injection token that can be used to inject instances of `MatRadioGroup`. It serves as\n * alternative token to the actual `MatRadioGroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_RADIO_GROUP = new InjectionToken('MatRadioGroup');\nconst MAT_RADIO_DEFAULT_OPTIONS = new InjectionToken('mat-radio-default-options', {\n  providedIn: 'root',\n  factory: MAT_RADIO_DEFAULT_OPTIONS_FACTORY\n});\nfunction MAT_RADIO_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent'\n  };\n}\n/**\n * A group of radio buttons. May contain one or more `<mat-radio-button>` elements.\n */\nclass MatRadioGroup {\n  /** Name of the radio button group. All radio buttons inside this group will use this name. */\n  get name() {\n    return this._name;\n  }\n  set name(value) {\n    this._name = value;\n    this._updateRadioButtonNames();\n  }\n  /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n  get labelPosition() {\n    return this._labelPosition;\n  }\n  set labelPosition(v) {\n    this._labelPosition = v === 'before' ? 'before' : 'after';\n    this._markRadiosForCheck();\n  }\n  /**\n   * Value for the radio-group. Should equal the value of the selected radio button if there is\n   * a corresponding radio button with a matching value. If there is not such a corresponding\n   * radio button, this value persists to be applied in case a new radio button is added with a\n   * matching value.\n   */\n  get value() {\n    return this._value;\n  }\n  set value(newValue) {\n    if (this._value !== newValue) {\n      // Set this before proceeding to ensure no circular loop occurs with selection.\n      this._value = newValue;\n      this._updateSelectedRadioFromValue();\n      this._checkSelectedRadioButton();\n    }\n  }\n  _checkSelectedRadioButton() {\n    if (this._selected && !this._selected.checked) {\n      this._selected.checked = true;\n    }\n  }\n  /**\n   * The currently selected radio button. If set to a new radio button, the radio group value\n   * will be updated to match the new selected button.\n   */\n  get selected() {\n    return this._selected;\n  }\n  set selected(selected) {\n    this._selected = selected;\n    this.value = selected ? selected.value : null;\n    this._checkSelectedRadioButton();\n  }\n  /** Whether the radio group is disabled */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    this._markRadiosForCheck();\n  }\n  /** Whether the radio group is required */\n  get required() {\n    return this._required;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n    this._markRadiosForCheck();\n  }\n  constructor(_changeDetector) {\n    this._changeDetector = _changeDetector;\n    /** Selected value for the radio group. */\n    this._value = null;\n    /** The HTML name attribute applied to radio buttons in this group. */\n    this._name = `mat-radio-group-${nextUniqueId++}`;\n    /** The currently selected radio button. Should match value. */\n    this._selected = null;\n    /** Whether the `value` has been set to its initial value. */\n    this._isInitialized = false;\n    /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n    this._labelPosition = 'after';\n    /** Whether the radio group is disabled. */\n    this._disabled = false;\n    /** Whether the radio group is required. */\n    this._required = false;\n    /** The method to be called in order to update ngModel */\n    this._controlValueAccessorChangeFn = () => {};\n    /**\n     * onTouch function registered via registerOnTouch (ControlValueAccessor).\n     * @docs-private\n     */\n    this.onTouched = () => {};\n    /**\n     * Event emitted when the group value changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * a radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    this.change = new EventEmitter();\n  }\n  /**\n   * Initialize properties once content children are available.\n   * This allows us to propagate relevant attributes to associated buttons.\n   */\n  ngAfterContentInit() {\n    // Mark this component as initialized in AfterContentInit because the initial value can\n    // possibly be set by NgModel on MatRadioGroup, and it is possible that the OnInit of the\n    // NgModel occurs *after* the OnInit of the MatRadioGroup.\n    this._isInitialized = true;\n    // Clear the `selected` button when it's destroyed since the tabindex of the rest of the\n    // buttons depends on it. Note that we don't clear the `value`, because the radio button\n    // may be swapped out with a similar one and there are some internal apps that depend on\n    // that behavior.\n    this._buttonChanges = this._radios.changes.subscribe(() => {\n      if (this.selected && !this._radios.find(radio => radio === this.selected)) {\n        this._selected = null;\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._buttonChanges?.unsubscribe();\n  }\n  /**\n   * Mark this group as being \"touched\" (for ngModel). Meant to be called by the contained\n   * radio buttons upon their blur.\n   */\n  _touch() {\n    if (this.onTouched) {\n      this.onTouched();\n    }\n  }\n  _updateRadioButtonNames() {\n    if (this._radios) {\n      this._radios.forEach(radio => {\n        radio.name = this.name;\n        radio._markForCheck();\n      });\n    }\n  }\n  /** Updates the `selected` radio button from the internal _value state. */\n  _updateSelectedRadioFromValue() {\n    // If the value already matches the selected radio, do nothing.\n    const isAlreadySelected = this._selected !== null && this._selected.value === this._value;\n    if (this._radios && !isAlreadySelected) {\n      this._selected = null;\n      this._radios.forEach(radio => {\n        radio.checked = this.value === radio.value;\n        if (radio.checked) {\n          this._selected = radio;\n        }\n      });\n    }\n  }\n  /** Dispatch change event with current selection and group value. */\n  _emitChangeEvent() {\n    if (this._isInitialized) {\n      this.change.emit(new MatRadioChange(this._selected, this._value));\n    }\n  }\n  _markRadiosForCheck() {\n    if (this._radios) {\n      this._radios.forEach(radio => radio._markForCheck());\n    }\n  }\n  /**\n   * Sets the model value. Implemented as part of ControlValueAccessor.\n   * @param value\n   */\n  writeValue(value) {\n    this.value = value;\n    this._changeDetector.markForCheck();\n  }\n  /**\n   * Registers a callback to be triggered when the model value changes.\n   * Implemented as part of ControlValueAccessor.\n   * @param fn Callback to be registered.\n   */\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  /**\n   * Registers a callback to be triggered when the control is touched.\n   * Implemented as part of ControlValueAccessor.\n   * @param fn Callback to be registered.\n   */\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  /**\n   * Sets the disabled state of the control. Implemented as a part of ControlValueAccessor.\n   * @param isDisabled Whether the control should be disabled.\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetector.markForCheck();\n  }\n  static {\n    this.ɵfac = function MatRadioGroup_Factory(t) {\n      return new (t || MatRadioGroup)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRadioGroup,\n      selectors: [[\"mat-radio-group\"]],\n      contentQueries: function MatRadioGroup_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatRadioButton, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._radios = _t);\n        }\n      },\n      hostAttrs: [\"role\", \"radiogroup\", 1, \"mat-mdc-radio-group\"],\n      inputs: {\n        color: \"color\",\n        name: \"name\",\n        labelPosition: \"labelPosition\",\n        value: \"value\",\n        selected: \"selected\",\n        disabled: \"disabled\",\n        required: \"required\"\n      },\n      outputs: {\n        change: \"change\"\n      },\n      exportAs: [\"matRadioGroup\"],\n      features: [i0.ɵɵProvidersFeature([MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, {\n        provide: MAT_RADIO_GROUP,\n        useExisting: MatRadioGroup\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRadioGroup, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-radio-group',\n      exportAs: 'matRadioGroup',\n      providers: [MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, {\n        provide: MAT_RADIO_GROUP,\n        useExisting: MatRadioGroup\n      }],\n      host: {\n        'role': 'radiogroup',\n        'class': 'mat-mdc-radio-group'\n      }\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    change: [{\n      type: Output\n    }],\n    _radios: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatRadioButton), {\n        descendants: true\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }]\n  });\n})();\n// Boilerplate for applying mixins to MatRadioButton.\n/** @docs-private */\nclass MatRadioButtonBase {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n}\nconst _MatRadioButtonMixinBase = mixinDisableRipple(mixinTabIndex(MatRadioButtonBase));\nclass MatRadioButton extends _MatRadioButtonMixinBase {\n  /** Whether this radio button is checked. */\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    const newCheckedState = coerceBooleanProperty(value);\n    if (this._checked !== newCheckedState) {\n      this._checked = newCheckedState;\n      if (newCheckedState && this.radioGroup && this.radioGroup.value !== this.value) {\n        this.radioGroup.selected = this;\n      } else if (!newCheckedState && this.radioGroup && this.radioGroup.value === this.value) {\n        // When unchecking the selected radio button, update the selected radio\n        // property on the group.\n        this.radioGroup.selected = null;\n      }\n      if (newCheckedState) {\n        // Notify all radio buttons with the same name to un-check.\n        this._radioDispatcher.notify(this.id, this.name);\n      }\n      this._changeDetector.markForCheck();\n    }\n  }\n  /** The value of this radio button. */\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    if (this._value !== value) {\n      this._value = value;\n      if (this.radioGroup !== null) {\n        if (!this.checked) {\n          // Update checked when the value changed to match the radio group's value\n          this.checked = this.radioGroup.value === value;\n        }\n        if (this.checked) {\n          this.radioGroup.selected = this;\n        }\n      }\n    }\n  }\n  /** Whether the label should appear after or before the radio button. Defaults to 'after' */\n  get labelPosition() {\n    return this._labelPosition || this.radioGroup && this.radioGroup.labelPosition || 'after';\n  }\n  set labelPosition(value) {\n    this._labelPosition = value;\n  }\n  /** Whether the radio button is disabled. */\n  get disabled() {\n    return this._disabled || this.radioGroup !== null && this.radioGroup.disabled;\n  }\n  set disabled(value) {\n    this._setDisabled(coerceBooleanProperty(value));\n  }\n  /** Whether the radio button is required. */\n  get required() {\n    return this._required || this.radioGroup && this.radioGroup.required;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n  }\n  /** Theme color of the radio button. */\n  get color() {\n    // As per Material design specifications the selection control radio should use the accent color\n    // palette by default. https://material.io/guidelines/components/selection-controls.html\n    return this._color || this.radioGroup && this.radioGroup.color || this._providerOverride && this._providerOverride.color || 'accent';\n  }\n  set color(newValue) {\n    this._color = newValue;\n  }\n  /** ID of the native input element inside `<mat-radio-button>` */\n  get inputId() {\n    return `${this.id || this._uniqueId}-input`;\n  }\n  constructor(radioGroup, elementRef, _changeDetector, _focusMonitor, _radioDispatcher, animationMode, _providerOverride, tabIndex) {\n    super(elementRef);\n    this._changeDetector = _changeDetector;\n    this._focusMonitor = _focusMonitor;\n    this._radioDispatcher = _radioDispatcher;\n    this._providerOverride = _providerOverride;\n    this._uniqueId = `mat-radio-${++nextUniqueId}`;\n    /** The unique ID for the radio button. */\n    this.id = this._uniqueId;\n    /**\n     * Event emitted when the checked state of this radio button changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * the radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    this.change = new EventEmitter();\n    /** Whether this radio is checked. */\n    this._checked = false;\n    /** Value assigned to this radio. */\n    this._value = null;\n    /** Unregister function for _radioDispatcher */\n    this._removeUniqueSelectionListener = () => {};\n    // Assertions. Ideally these should be stripped out by the compiler.\n    // TODO(jelbourn): Assert that there's no name binding AND a parent radio group.\n    this.radioGroup = radioGroup;\n    this._noopAnimations = animationMode === 'NoopAnimations';\n    if (tabIndex) {\n      this.tabIndex = coerceNumberProperty(tabIndex, 0);\n    }\n  }\n  /** Focuses the radio button. */\n  focus(options, origin) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._inputElement, origin, options);\n    } else {\n      this._inputElement.nativeElement.focus(options);\n    }\n  }\n  /**\n   * Marks the radio button as needing checking for change detection.\n   * This method is exposed because the parent radio group will directly\n   * update bound properties of the radio button.\n   */\n  _markForCheck() {\n    // When group value changes, the button will not be notified. Use `markForCheck` to explicit\n    // update radio button's status\n    this._changeDetector.markForCheck();\n  }\n  ngOnInit() {\n    if (this.radioGroup) {\n      // If the radio is inside a radio group, determine if it should be checked\n      this.checked = this.radioGroup.value === this._value;\n      if (this.checked) {\n        this.radioGroup.selected = this;\n      }\n      // Copy name from parent radio group\n      this.name = this.radioGroup.name;\n    }\n    this._removeUniqueSelectionListener = this._radioDispatcher.listen((id, name) => {\n      if (id !== this.id && name === this.name) {\n        this.checked = false;\n      }\n    });\n  }\n  ngDoCheck() {\n    this._updateTabIndex();\n  }\n  ngAfterViewInit() {\n    this._updateTabIndex();\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n      if (!focusOrigin && this.radioGroup) {\n        this.radioGroup._touch();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._removeUniqueSelectionListener();\n  }\n  /** Dispatch change event with current value. */\n  _emitChangeEvent() {\n    this.change.emit(new MatRadioChange(this, this._value));\n  }\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n  _onInputClick(event) {\n    // We have to stop propagation for click events on the visual hidden input element.\n    // By default, when a user clicks on a label element, a generated click event will be\n    // dispatched on the associated input element. Since we are using a label element as our\n    // root container, the click event on the `radio-button` will be executed twice.\n    // The real click event will bubble up, and the generated click event also tries to bubble up.\n    // This will lead to multiple click events.\n    // Preventing bubbling for the second event will solve that issue.\n    event.stopPropagation();\n  }\n  /** Triggered when the radio button receives an interaction from the user. */\n  _onInputInteraction(event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n    if (!this.checked && !this.disabled) {\n      const groupValueChanged = this.radioGroup && this.value !== this.radioGroup.value;\n      this.checked = true;\n      this._emitChangeEvent();\n      if (this.radioGroup) {\n        this.radioGroup._controlValueAccessorChangeFn(this.value);\n        if (groupValueChanged) {\n          this.radioGroup._emitChangeEvent();\n        }\n      }\n    }\n  }\n  /** Triggered when the user clicks on the touch target. */\n  _onTouchTargetClick(event) {\n    this._onInputInteraction(event);\n    if (!this.disabled) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement.nativeElement.focus();\n    }\n  }\n  /** Sets the disabled state and marks for check if a change occurred. */\n  _setDisabled(value) {\n    if (this._disabled !== value) {\n      this._disabled = value;\n      this._changeDetector.markForCheck();\n    }\n  }\n  /** Gets the tabindex for the underlying input element. */\n  _updateTabIndex() {\n    const group = this.radioGroup;\n    let value;\n    // Implement a roving tabindex if the button is inside a group. For most cases this isn't\n    // necessary, because the browser handles the tab order for inputs inside a group automatically,\n    // but we need an explicitly higher tabindex for the selected button in order for things like\n    // the focus trap to pick it up correctly.\n    if (!group || !group.selected || this.disabled) {\n      value = this.tabIndex;\n    } else {\n      value = group.selected === this ? this.tabIndex : -1;\n    }\n    if (value !== this._previousTabIndex) {\n      // We have to set the tabindex directly on the DOM node, because it depends on\n      // the selected state which is prone to \"changed after checked errors\".\n      const input = this._inputElement?.nativeElement;\n      if (input) {\n        input.setAttribute('tabindex', value + '');\n        this._previousTabIndex = value;\n      }\n    }\n  }\n  static {\n    this.ɵfac = function MatRadioButton_Factory(t) {\n      return new (t || MatRadioButton)(i0.ɵɵdirectiveInject(MAT_RADIO_GROUP, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i2.UniqueSelectionDispatcher), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_RADIO_DEFAULT_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatRadioButton,\n      selectors: [[\"mat-radio-button\"]],\n      viewQuery: function MatRadioButton_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-radio-button\"],\n      hostVars: 15,\n      hostBindings: function MatRadioButton_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatRadioButton_focus_HostBindingHandler() {\n            return ctx._inputElement.nativeElement.focus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n          i0.ɵɵclassProp(\"mat-primary\", ctx.color === \"primary\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"mat-mdc-radio-checked\", ctx.checked)(\"_mat-animation-noopable\", ctx._noopAnimations);\n        }\n      },\n      inputs: {\n        disableRipple: \"disableRipple\",\n        tabIndex: \"tabIndex\",\n        id: \"id\",\n        name: \"name\",\n        ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [i0.ɵɵInputFlags.None, \"aria-describedby\", \"ariaDescribedby\"],\n        checked: \"checked\",\n        value: \"value\",\n        labelPosition: \"labelPosition\",\n        disabled: \"disabled\",\n        required: \"required\",\n        color: \"color\"\n      },\n      outputs: {\n        change: \"change\"\n      },\n      exportAs: [\"matRadioButton\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      ngContentSelectors: _c1,\n      decls: 13,\n      vars: 17,\n      consts: [[\"formField\", \"\"], [\"input\", \"\"], [1, \"mdc-form-field\"], [1, \"mdc-radio\"], [1, \"mat-mdc-radio-touch-target\", 3, \"click\"], [\"type\", \"radio\", 1, \"mdc-radio__native-control\", 3, \"change\", \"id\", \"checked\", \"disabled\", \"required\"], [1, \"mdc-radio__background\"], [1, \"mdc-radio__outer-circle\"], [1, \"mdc-radio__inner-circle\"], [\"mat-ripple\", \"\", 1, \"mat-radio-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mat-ripple-element\", \"mat-radio-persistent-ripple\"], [1, \"mdc-label\", 3, \"for\"]],\n      template: function MatRadioButton_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 2, 0)(2, \"div\", 3)(3, \"div\", 4);\n          i0.ɵɵlistener(\"click\", function MatRadioButton_Template_div_click_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onTouchTargetClick($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"input\", 5, 1);\n          i0.ɵɵlistener(\"change\", function MatRadioButton_Template_input_change_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onInputInteraction($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵelement(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 9);\n          i0.ɵɵelement(10, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"label\", 11);\n          i0.ɵɵprojection(12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const formField_r2 = i0.ɵɵreference(1);\n          i0.ɵɵclassProp(\"mdc-form-field--align-end\", ctx.labelPosition == \"before\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"mdc-radio--disabled\", ctx.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"id\", ctx.inputId)(\"checked\", ctx.checked)(\"disabled\", ctx.disabled)(\"required\", ctx.required);\n          i0.ɵɵattribute(\"name\", ctx.name)(\"value\", ctx.value)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"matRippleTrigger\", formField_r2)(\"matRippleDisabled\", ctx._isRippleDisabled())(\"matRippleCentered\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"for\", ctx.inputId);\n        }\n      },\n      dependencies: [i3.MatRipple],\n      styles: [\".mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color}.mdc-radio[hidden]{display:none}.mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1}.mdc-radio--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-radio--touch .mdc-radio__native-control{top:calc((40px - 48px) / 2);right:calc((40px - 48px) / 2);left:calc((40px - 48px) / 2);width:48px;height:48px}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{border-color:CanvasText}}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{border-color:CanvasText}}.mdc-radio__native-control:checked+.mdc-radio__background,.mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio--disabled{cursor:default;pointer-events:none}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:disabled+.mdc-radio__background,[aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background{cursor:default}.mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{padding:calc((var(--mdc-radio-state-layer-size) - 20px) / 2)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{top:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);left:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control{top:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);right:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);left:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, black)}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}[dir=rtl] .mat-mdc-radio-touch-target{left:0;right:50%;transform:translate(50%, -50%)}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRadioButton, [{\n    type: Component,\n    args: [{\n      selector: 'mat-radio-button',\n      host: {\n        'class': 'mat-mdc-radio-button',\n        '[attr.id]': 'id',\n        '[class.mat-primary]': 'color === \"primary\"',\n        '[class.mat-accent]': 'color === \"accent\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class.mat-mdc-radio-checked]': 'checked',\n        '[class._mat-animation-noopable]': '_noopAnimations',\n        // Needs to be removed since it causes some a11y issues (see #21266).\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.aria-describedby]': 'null',\n        // Note: under normal conditions focus shouldn't land on this element, however it may be\n        // programmatically set, for example inside of a focus trap, in this case we want to forward\n        // the focus to the native element.\n        '(focus)': '_inputElement.nativeElement.focus()'\n      },\n      inputs: ['disableRipple', 'tabIndex'],\n      exportAs: 'matRadioButton',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"mdc-form-field\\\" #formField\\n     [class.mdc-form-field--align-end]=\\\"labelPosition == 'before'\\\">\\n  <div class=\\\"mdc-radio\\\" [class.mdc-radio--disabled]=\\\"disabled\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-radio-touch-target\\\" (click)=\\\"_onTouchTargetClick($event)\\\"></div>\\n    <input #input class=\\\"mdc-radio__native-control\\\" type=\\\"radio\\\"\\n           [id]=\\\"inputId\\\"\\n           [checked]=\\\"checked\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [required]=\\\"required\\\"\\n           [attr.aria-label]=\\\"ariaLabel\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           (change)=\\\"_onInputInteraction($event)\\\">\\n    <div class=\\\"mdc-radio__background\\\">\\n      <div class=\\\"mdc-radio__outer-circle\\\"></div>\\n      <div class=\\\"mdc-radio__inner-circle\\\"></div>\\n    </div>\\n    <div mat-ripple class=\\\"mat-radio-ripple mat-mdc-focus-indicator\\\"\\n         [matRippleTrigger]=\\\"formField\\\"\\n         [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n         [matRippleCentered]=\\\"true\\\">\\n      <div class=\\\"mat-ripple-element mat-radio-persistent-ripple\\\"></div>\\n    </div>\\n  </div>\\n  <label class=\\\"mdc-label\\\" [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\",\n      styles: [\".mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color}.mdc-radio[hidden]{display:none}.mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1}.mdc-radio--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-radio--touch .mdc-radio__native-control{top:calc((40px - 48px) / 2);right:calc((40px - 48px) / 2);left:calc((40px - 48px) / 2);width:48px;height:48px}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{border-color:CanvasText}}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{border-color:CanvasText}}.mdc-radio__native-control:checked+.mdc-radio__background,.mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio--disabled{cursor:default;pointer-events:none}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:disabled+.mdc-radio__background,[aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background{cursor:default}.mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{padding:calc((var(--mdc-radio-state-layer-size) - 20px) / 2)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{top:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);left:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control{top:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);right:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);left:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, black)}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}[dir=rtl] .mat-mdc-radio-touch-target{left:0;right:50%;transform:translate(50%, -50%)}\"]\n    }]\n  }], () => [{\n    type: MatRadioGroup,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_RADIO_GROUP]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.FocusMonitor\n  }, {\n    type: i2.UniqueSelectionDispatcher\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_RADIO_DEFAULT_OPTIONS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }], {\n    id: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    checked: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    _inputElement: [{\n      type: ViewChild,\n      args: ['input']\n    }]\n  });\n})();\nclass MatRadioModule {\n  static {\n    this.ɵfac = function MatRadioModule_Factory(t) {\n      return new (t || MatRadioModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatRadioModule,\n      declarations: [MatRadioGroup, MatRadioButton],\n      imports: [MatCommonModule, CommonModule, MatRippleModule],\n      exports: [MatCommonModule, MatRadioGroup, MatRadioButton]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CommonModule, MatRippleModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRadioModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CommonModule, MatRippleModule],\n      exports: [MatCommonModule, MatRadioGroup, MatRadioButton],\n      declarations: [MatRadioGroup, MatRadioButton]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_RADIO_DEFAULT_OPTIONS, MAT_RADIO_DEFAULT_OPTIONS_FACTORY, MAT_RADIO_GROUP, MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, MatRadioButton, MatRadioChange, MatRadioGroup, MatRadioModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,GAAG;AAChB,IAAI,eAAe;AAEnB,IAAM,iBAAN,MAAqB;AAAA,EACnB,YACA,QACA,OAAO;AACL,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AACF;AAMA,IAAM,yCAAyC;AAAA,EAC7C,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,aAAa;AAAA,EAC3C,OAAO;AACT;AAMA,IAAM,kBAAkB,IAAI,eAAe,eAAe;AAC1D,IAAM,4BAA4B,IAAI,eAAe,6BAA6B;AAAA,EAChF,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AACD,SAAS,oCAAoC;AAC3C,SAAO;AAAA,IACL,OAAO;AAAA,EACT;AACF;AAIA,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA,EAElB,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,QAAQ;AACb,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,GAAG;AACnB,SAAK,iBAAiB,MAAM,WAAW,WAAW;AAClD,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,UAAU;AAClB,QAAI,KAAK,WAAW,UAAU;AAE5B,WAAK,SAAS;AACd,WAAK,8BAA8B;AACnC,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AAAA,EACA,4BAA4B;AAC1B,QAAI,KAAK,aAAa,CAAC,KAAK,UAAU,SAAS;AAC7C,WAAK,UAAU,UAAU;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,UAAU;AACrB,SAAK,YAAY;AACjB,SAAK,QAAQ,WAAW,SAAS,QAAQ;AACzC,SAAK,0BAA0B;AAAA,EACjC;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAC5C,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAC5C,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,YAAY,iBAAiB;AAC3B,SAAK,kBAAkB;AAEvB,SAAK,SAAS;AAEd,SAAK,QAAQ,mBAAmB,cAAc;AAE9C,SAAK,YAAY;AAEjB,SAAK,iBAAiB;AAEtB,SAAK,iBAAiB;AAEtB,SAAK,YAAY;AAEjB,SAAK,YAAY;AAEjB,SAAK,gCAAgC,MAAM;AAAA,IAAC;AAK5C,SAAK,YAAY,MAAM;AAAA,IAAC;AAMxB,SAAK,SAAS,IAAI,aAAa;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AAInB,SAAK,iBAAiB;AAKtB,SAAK,iBAAiB,KAAK,QAAQ,QAAQ,UAAU,MAAM;AACzD,UAAI,KAAK,YAAY,CAAC,KAAK,QAAQ,KAAK,WAAS,UAAU,KAAK,QAAQ,GAAG;AACzE,aAAK,YAAY;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,YAAY;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AACxB,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,QAAQ,WAAS;AAC5B,cAAM,OAAO,KAAK;AAClB,cAAM,cAAc;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,gCAAgC;AAE9B,UAAM,oBAAoB,KAAK,cAAc,QAAQ,KAAK,UAAU,UAAU,KAAK;AACnF,QAAI,KAAK,WAAW,CAAC,mBAAmB;AACtC,WAAK,YAAY;AACjB,WAAK,QAAQ,QAAQ,WAAS;AAC5B,cAAM,UAAU,KAAK,UAAU,MAAM;AACrC,YAAI,MAAM,SAAS;AACjB,eAAK,YAAY;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB;AACjB,QAAI,KAAK,gBAAgB;AACvB,WAAK,OAAO,KAAK,IAAI,eAAe,KAAK,WAAW,KAAK,MAAM,CAAC;AAAA,IAClE;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,QAAQ,WAAS,MAAM,cAAc,CAAC;AAAA,IACrD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAChB,SAAK,QAAQ;AACb,SAAK,gBAAgB,aAAa;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,IAAI;AACnB,SAAK,gCAAgC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAChB,SAAK,gBAAgB,aAAa;AAAA,EACpC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAkB,kBAAqB,iBAAiB,CAAC;AAAA,IAC5E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,gBAAgB,SAAS,6BAA6B,IAAI,KAAK,UAAU;AACvE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,gBAAgB,CAAC;AAAA,QAC/C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU;AAAA,QAC7D;AAAA,MACF;AAAA,MACA,WAAW,CAAC,QAAQ,cAAc,GAAG,qBAAqB;AAAA,MAC1D,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,eAAe;AAAA,QACf,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,eAAe;AAAA,MAC1B,UAAU,CAAI,mBAAmB,CAAC,wCAAwC;AAAA,QACxE,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,CAAC;AAAA,IACL,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,wCAAwC;AAAA,QAClD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,MAAM,cAAc,GAAG;AAAA,QACvC,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,qBAAN,MAAyB;AAAA,EACvB,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AACF;AACA,IAAM,2BAA2B,mBAAmB,cAAc,kBAAkB,CAAC;AACrF,IAAM,iBAAN,MAAM,wBAAuB,yBAAyB;AAAA;AAAA,EAEpD,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,UAAM,kBAAkB,sBAAsB,KAAK;AACnD,QAAI,KAAK,aAAa,iBAAiB;AACrC,WAAK,WAAW;AAChB,UAAI,mBAAmB,KAAK,cAAc,KAAK,WAAW,UAAU,KAAK,OAAO;AAC9E,aAAK,WAAW,WAAW;AAAA,MAC7B,WAAW,CAAC,mBAAmB,KAAK,cAAc,KAAK,WAAW,UAAU,KAAK,OAAO;AAGtF,aAAK,WAAW,WAAW;AAAA,MAC7B;AACA,UAAI,iBAAiB;AAEnB,aAAK,iBAAiB,OAAO,KAAK,IAAI,KAAK,IAAI;AAAA,MACjD;AACA,WAAK,gBAAgB,aAAa;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,QAAI,KAAK,WAAW,OAAO;AACzB,WAAK,SAAS;AACd,UAAI,KAAK,eAAe,MAAM;AAC5B,YAAI,CAAC,KAAK,SAAS;AAEjB,eAAK,UAAU,KAAK,WAAW,UAAU;AAAA,QAC3C;AACA,YAAI,KAAK,SAAS;AAChB,eAAK,WAAW,WAAW;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,gBAAgB;AAClB,WAAO,KAAK,kBAAkB,KAAK,cAAc,KAAK,WAAW,iBAAiB;AAAA,EACpF;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,eAAe,QAAQ,KAAK,WAAW;AAAA,EACvE;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,aAAa,sBAAsB,KAAK,CAAC;AAAA,EAChD;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,cAAc,KAAK,WAAW;AAAA,EAC9D;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAAA,EAC9C;AAAA;AAAA,EAEA,IAAI,QAAQ;AAGV,WAAO,KAAK,UAAU,KAAK,cAAc,KAAK,WAAW,SAAS,KAAK,qBAAqB,KAAK,kBAAkB,SAAS;AAAA,EAC9H;AAAA,EACA,IAAI,MAAM,UAAU;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,GAAG,KAAK,MAAM,KAAK,SAAS;AAAA,EACrC;AAAA,EACA,YAAY,YAAY,YAAY,iBAAiB,eAAe,kBAAkB,eAAe,mBAAmB,UAAU;AAChI,UAAM,UAAU;AAChB,SAAK,kBAAkB;AACvB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AACxB,SAAK,oBAAoB;AACzB,SAAK,YAAY,aAAa,EAAE,YAAY;AAE5C,SAAK,KAAK,KAAK;AAMf,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,WAAW;AAEhB,SAAK,SAAS;AAEd,SAAK,iCAAiC,MAAM;AAAA,IAAC;AAG7C,SAAK,aAAa;AAClB,SAAK,kBAAkB,kBAAkB;AACzC,QAAI,UAAU;AACZ,WAAK,WAAW,qBAAqB,UAAU,CAAC;AAAA,IAClD;AAAA,EACF;AAAA;AAAA,EAEA,MAAM,SAAS,QAAQ;AACrB,QAAI,QAAQ;AACV,WAAK,cAAc,SAAS,KAAK,eAAe,QAAQ,OAAO;AAAA,IACjE,OAAO;AACL,WAAK,cAAc,cAAc,MAAM,OAAO;AAAA,IAChD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AAGd,SAAK,gBAAgB,aAAa;AAAA,EACpC;AAAA,EACA,WAAW;AACT,QAAI,KAAK,YAAY;AAEnB,WAAK,UAAU,KAAK,WAAW,UAAU,KAAK;AAC9C,UAAI,KAAK,SAAS;AAChB,aAAK,WAAW,WAAW;AAAA,MAC7B;AAEA,WAAK,OAAO,KAAK,WAAW;AAAA,IAC9B;AACA,SAAK,iCAAiC,KAAK,iBAAiB,OAAO,CAAC,IAAI,SAAS;AAC/E,UAAI,OAAO,KAAK,MAAM,SAAS,KAAK,MAAM;AACxC,aAAK,UAAU;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY;AACV,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB;AAChB,SAAK,gBAAgB;AACrB,SAAK,cAAc,QAAQ,KAAK,aAAa,IAAI,EAAE,UAAU,iBAAe;AAC1E,UAAI,CAAC,eAAe,KAAK,YAAY;AACnC,aAAK,WAAW,OAAO;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,eAAe,KAAK,WAAW;AAClD,SAAK,+BAA+B;AAAA,EACtC;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,OAAO,KAAK,IAAI,eAAe,MAAM,KAAK,MAAM,CAAC;AAAA,EACxD;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,iBAAiB,KAAK;AAAA,EACpC;AAAA,EACA,cAAc,OAAO;AAQnB,UAAM,gBAAgB;AAAA,EACxB;AAAA;AAAA,EAEA,oBAAoB,OAAO;AAIzB,UAAM,gBAAgB;AACtB,QAAI,CAAC,KAAK,WAAW,CAAC,KAAK,UAAU;AACnC,YAAM,oBAAoB,KAAK,cAAc,KAAK,UAAU,KAAK,WAAW;AAC5E,WAAK,UAAU;AACf,WAAK,iBAAiB;AACtB,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,8BAA8B,KAAK,KAAK;AACxD,YAAI,mBAAmB;AACrB,eAAK,WAAW,iBAAiB;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB,OAAO;AACzB,SAAK,oBAAoB,KAAK;AAC9B,QAAI,CAAC,KAAK,UAAU;AAGlB,WAAK,cAAc,cAAc,MAAM;AAAA,IACzC;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,QAAI,KAAK,cAAc,OAAO;AAC5B,WAAK,YAAY;AACjB,WAAK,gBAAgB,aAAa;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,UAAM,QAAQ,KAAK;AACnB,QAAI;AAKJ,QAAI,CAAC,SAAS,CAAC,MAAM,YAAY,KAAK,UAAU;AAC9C,cAAQ,KAAK;AAAA,IACf,OAAO;AACL,cAAQ,MAAM,aAAa,OAAO,KAAK,WAAW;AAAA,IACpD;AACA,QAAI,UAAU,KAAK,mBAAmB;AAGpC,YAAM,QAAQ,KAAK,eAAe;AAClC,UAAI,OAAO;AACT,cAAM,aAAa,YAAY,QAAQ,EAAE;AACzC,aAAK,oBAAoB;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAmB,kBAAkB,iBAAiB,CAAC,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,yBAAyB,GAAM,kBAAkB,uBAAuB,CAAC,GAAM,kBAAkB,2BAA2B,CAAC,GAAM,kBAAkB,UAAU,CAAC;AAAA,IAC7X;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,WAAW,SAAS,qBAAqB,IAAI,KAAK;AAChD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,QACtE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,sBAAsB;AAAA,MACrC,UAAU;AAAA,MACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,0CAA0C;AACxE,mBAAO,IAAI,cAAc,cAAc,MAAM;AAAA,UAC/C,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,MAAM,IAAI,EAAE,EAAE,YAAY,IAAI,EAAE,cAAc,IAAI,EAAE,mBAAmB,IAAI,EAAE,oBAAoB,IAAI;AACpH,UAAG,YAAY,eAAe,IAAI,UAAU,SAAS,EAAE,cAAc,IAAI,UAAU,QAAQ,EAAE,YAAY,IAAI,UAAU,MAAM,EAAE,yBAAyB,IAAI,OAAO,EAAE,2BAA2B,IAAI,eAAe;AAAA,QACrN;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,eAAe;AAAA,QACf,UAAU;AAAA,QACV,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,WAAW,CAAI,WAAa,MAAM,cAAc,WAAW;AAAA,QAC3D,gBAAgB,CAAI,WAAa,MAAM,mBAAmB,gBAAgB;AAAA,QAC1E,iBAAiB,CAAI,WAAa,MAAM,oBAAoB,iBAAiB;AAAA,QAC7E,SAAS;AAAA,QACT,OAAO;AAAA,QACP,eAAe;AAAA,QACf,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,gBAAgB;AAAA,MAC3B,UAAU,CAAI,0BAA0B;AAAA,MACxC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,8BAA8B,GAAG,OAAO,GAAG,CAAC,QAAQ,SAAS,GAAG,6BAA6B,GAAG,UAAU,MAAM,WAAW,YAAY,UAAU,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,cAAc,IAAI,GAAG,oBAAoB,2BAA2B,GAAG,oBAAoB,qBAAqB,mBAAmB,GAAG,CAAC,GAAG,sBAAsB,6BAA6B,GAAG,CAAC,GAAG,aAAa,GAAG,KAAK,CAAC;AAAA,MACriB,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1D,UAAG,WAAW,SAAS,SAAS,6CAA6C,QAAQ;AACnF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,oBAAoB,MAAM,CAAC;AAAA,UACvD,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,UAAG,WAAW,UAAU,SAAS,gDAAgD,QAAQ;AACvF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,oBAAoB,MAAM,CAAC;AAAA,UACvD,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,UAAU,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACrC,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,UAAU,IAAI,OAAO,EAAE;AAC1B,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,IAAI,SAAS,EAAE;AACjC,UAAG,aAAa,EAAE;AAClB,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,eAAkB,YAAY,CAAC;AACrC,UAAG,YAAY,6BAA6B,IAAI,iBAAiB,QAAQ;AACzE,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,uBAAuB,IAAI,QAAQ;AAClD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,MAAM,IAAI,OAAO,EAAE,WAAW,IAAI,OAAO,EAAE,YAAY,IAAI,QAAQ,EAAE,YAAY,IAAI,QAAQ;AAC3G,UAAG,YAAY,QAAQ,IAAI,IAAI,EAAE,SAAS,IAAI,KAAK,EAAE,cAAc,IAAI,SAAS,EAAE,mBAAmB,IAAI,cAAc,EAAE,oBAAoB,IAAI,eAAe;AAChK,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,oBAAoB,YAAY,EAAE,qBAAqB,IAAI,kBAAkB,CAAC,EAAE,qBAAqB,IAAI;AACvH,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,OAAO,IAAI,OAAO;AAAA,QAClC;AAAA,MACF;AAAA,MACA,cAAc,CAAI,SAAS;AAAA,MAC3B,QAAQ,CAAC,o3WAA03W;AAAA,MACn4W,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,aAAa;AAAA,QACb,uBAAuB;AAAA,QACvB,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,iCAAiC;AAAA,QACjC,mCAAmC;AAAA;AAAA,QAEnC,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA;AAAA;AAAA;AAAA,QAI3B,WAAW;AAAA,MACb;AAAA,MACA,QAAQ,CAAC,iBAAiB,UAAU;AAAA,MACpC,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,o3WAA03W;AAAA,IACr4W,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,eAAe,cAAc;AAAA,MAC5C,SAAS,CAAC,iBAAiB,cAAc,eAAe;AAAA,MACxD,SAAS,CAAC,iBAAiB,eAAe,cAAc;AAAA,IAC1D,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,iBAAiB,cAAc,iBAAiB,eAAe;AAAA,IAC3E,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,cAAc,eAAe;AAAA,MACxD,SAAS,CAAC,iBAAiB,eAAe,cAAc;AAAA,MACxD,cAAc,CAAC,eAAe,cAAc;AAAA,IAC9C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}