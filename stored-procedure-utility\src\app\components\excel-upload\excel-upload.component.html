<mat-card class="upload-card">
  <mat-card-header>
    <mat-card-title>
      <mat-icon>upload_file</mat-icon>
      Excel File Upload
    </mat-card-title>
    <mat-card-subtitle>
      Upload an Excel file (.xlsx, .xls) or CSV file to process
    </mat-card-subtitle>
  </mat-card-header>
  
  <mat-card-content>
    <!-- File Upload Area -->
    <div 
      class="upload-area"
      [class.drag-over]="isDragOver"
      [class.has-file]="excelData"
      (dragover)="onDragOver($event)"
      (dragleave)="onDragLeave($event)"
      (drop)="onDrop($event)"
      (click)="triggerFileInput()">
      
      <!-- Hidden File Input -->
      <input 
        type="file" 
        id="fileInput"
        accept=".xlsx,.xls,.csv"
        (change)="onFileSelected($event)"
        style="display: none;">

      <!-- Upload Content -->
      <div *ngIf="!excelData && !isLoading" class="upload-content">
        <mat-icon class="upload-icon">cloud_upload</mat-icon>
        <h3>Drop Excel file here or click to browse</h3>
        <p>Supported formats: .xlsx, .xls, .csv (Max size: 10MB)</p>
        <button mat-raised-button color="primary" class="browse-button">
          <mat-icon>folder_open</mat-icon>
          Browse Files
        </button>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading" class="loading-content">
        <mat-spinner diameter="50"></mat-spinner>
        <h3>Processing Excel file...</h3>
        <p>Please wait while we parse your file</p>
      </div>

      <!-- File Loaded State -->
      <div *ngIf="excelData && !isLoading" class="file-loaded-content">
        <mat-icon class="success-icon">check_circle</mat-icon>
        <h3>{{excelData.fileName}}</h3>
        <p>{{excelData.totalRows}} rows • {{excelData.columns.length}} columns</p>
        <div class="file-actions">
          <button mat-stroked-button (click)="clearFile(); $event.stopPropagation()">
            <mat-icon>clear</mat-icon>
            Remove File
          </button>
          <button mat-raised-button color="primary" (click)="triggerFileInput(); $event.stopPropagation()">
            <mat-icon>swap_horiz</mat-icon>
            Replace File
          </button>
        </div>
      </div>
    </div>

    <!-- Excel Data Preview -->
    <div *ngIf="excelData && !isLoading" class="data-preview">
      <h3>Column Information</h3>
      <p class="preview-description">
        Preview of columns detected in your Excel file. Data types are automatically inferred.
      </p>
      
      <table mat-table [dataSource]="excelData.columns" class="columns-table">
        
        <!-- Column Name -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Column Name</th>
          <td mat-cell *matCellDef="let column">
            <strong>{{column.name}}</strong>
            <span class="column-index">(Column {{column.index + 1}})</span>
          </td>
        </ng-container>

        <!-- Data Type -->
        <ng-container matColumnDef="dataType">
          <th mat-header-cell *matHeaderCellDef>Detected Type</th>
          <td mat-cell *matCellDef="let column">
            <mat-chip-set>
              <mat-chip [color]="getDataTypeColor(column.dataType)">
                {{column.dataType}}
              </mat-chip>
            </mat-chip-set>
          </td>
        </ng-container>

        <!-- Sample Values -->
        <ng-container matColumnDef="sampleValues">
          <th mat-header-cell *matHeaderCellDef>Sample Values</th>
          <td mat-cell *matCellDef="let column">
            <span class="sample-values">
              {{getSampleValuesDisplay(column.sampleValues)}}
            </span>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <!-- File Statistics -->
      <div class="file-stats">
        <div class="stat-item">
          <mat-icon>table_chart</mat-icon>
          <span>{{excelData.totalRows}} data rows</span>
        </div>
        <div class="stat-item">
          <mat-icon>view_column</mat-icon>
          <span>{{excelData.columns.length}} columns</span>
        </div>
        <div class="stat-item">
          <mat-icon>description</mat-icon>
          <span>Worksheet: {{excelData.worksheetName}}</span>
        </div>
      </div>
    </div>
  </mat-card-content>
</mat-card>
