.upload-card {
  margin: 20px;
  max-width: 1200px;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: #2196f3;
  background-color: #f0f8ff;
}

.upload-area.drag-over {
  border-color: #4caf50;
  background-color: #e8f5e8;
  transform: scale(1.02);
}

.upload-area.has-file {
  border-color: #4caf50;
  background-color: #e8f5e8;
}

.upload-content, .loading-content, .file-loaded-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: #666;
}

.success-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: #4caf50;
}

.upload-content h3, .loading-content h3, .file-loaded-content h3 {
  margin: 0;
  color: #333;
}

.upload-content p, .loading-content p, .file-loaded-content p {
  margin: 0;
  color: #666;
}

.browse-button {
  margin-top: 8px;
}

.file-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.data-preview {
  margin-top: 32px;
}

.data-preview h3 {
  margin-bottom: 8px;
  color: #333;
}

.preview-description {
  margin-bottom: 20px;
  color: #666;
  font-style: italic;
}

.columns-table {
  width: 100%;
  margin-top: 16px;
}

.columns-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.columns-table td {
  padding: 12px 8px;
}

.column-index {
  color: #666;
  font-size: 12px;
  margin-left: 8px;
}

.sample-values {
  color: #666;
  font-style: italic;
  font-size: 14px;
}

.file-stats {
  display: flex;
  gap: 24px;
  margin-top: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
}

.stat-item mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #2196f3;
}

/* Responsive design */
@media (max-width: 768px) {
  .upload-area {
    padding: 20px;
    min-height: 150px;
  }
  
  .upload-icon, .success-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
  }
  
  .file-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .file-actions button {
    width: 100%;
  }
  
  .columns-table {
    font-size: 14px;
  }
  
  .columns-table td, .columns-table th {
    padding: 8px 4px;
  }
  
  .file-stats {
    flex-direction: column;
    gap: 12px;
  }
}

/* Error snackbar styling */
::ng-deep .error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}
