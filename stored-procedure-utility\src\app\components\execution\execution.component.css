.execution-card {
  margin: 20px;
  max-width: 1200px;
}

.validation-section {
  margin-bottom: 24px;
  padding: 20px;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.validation-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.validation-header h3 {
  margin: 0;
  color: #333;
}

.validation-summary {
  margin: 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.validation-summary.valid {
  color: #2e7d32;
}

.validation-summary.invalid {
  color: #d32f2f;
}

.validation-errors {
  margin-top: 16px;
  padding: 16px;
  background-color: #ffebee;
  border-radius: 4px;
  border-left: 4px solid #f44336;
}

.validation-errors h4 {
  margin: 0 0 8px 0;
  color: #d32f2f;
}

.validation-errors ul {
  margin: 0;
  padding-left: 20px;
}

.validation-errors li {
  color: #d32f2f;
  margin-bottom: 4px;
}

.execution-info {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
}

.info-item mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: #2196f3;
}

.execution-controls {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.execute-button {
  min-width: 180px;
}

.execution-progress {
  margin: 24px 0;
  padding: 20px;
  background-color: #e3f2fd;
  border-radius: 8px;
  text-align: center;
}

.execution-progress h4 {
  margin: 0 0 16px 0;
  color: #1976d2;
}

.execution-progress p {
  margin: 16px 0 0 0;
  color: #666;
}

.execution-results {
  margin-top: 24px;
}

.execution-results h3 {
  margin-bottom: 20px;
  color: #333;
}

.results-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.summary-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-card.success {
  border-left: 4px solid #4caf50;
}

.summary-card.error {
  border-left: 4px solid #f44336;
}

.summary-card.total {
  border-left: 4px solid #2196f3;
}

.summary-card.time {
  border-left: 4px solid #ff9800;
}

.summary-card mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
}

.summary-card.success mat-icon {
  color: #4caf50;
}

.summary-card.error mat-icon {
  color: #f44336;
}

.summary-card.total mat-icon {
  color: #2196f3;
}

.summary-card.time mat-icon {
  color: #ff9800;
}

.summary-content {
  display: flex;
  flex-direction: column;
}

.summary-number {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.summary-label {
  font-size: 14px;
  color: #666;
}

.results-table-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.results-table {
  width: 100%;
}

.results-table th {
  background-color: #f5f5f5;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 1;
}

.results-table td {
  padding: 12px 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.message-text {
  font-size: 14px;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.error-message {
  color: #d32f2f;
}

.error-row {
  background-color: #ffebee;
}

/* Responsive design */
@media (max-width: 768px) {
  .execution-controls {
    flex-direction: column;
  }
  
  .execution-controls button {
    width: 100%;
  }
  
  .results-summary {
    grid-template-columns: 1fr;
  }
  
  .summary-card {
    padding: 16px;
  }
  
  .summary-card mat-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
  }
  
  .summary-number {
    font-size: 20px;
  }
  
  .results-table {
    font-size: 14px;
  }
  
  .results-table td, .results-table th {
    padding: 8px 4px;
  }
  
  .message-text {
    max-width: 150px;
  }
  
  .execution-info {
    gap: 12px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* Snackbar styling */
::ng-deep .success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

::ng-deep .warning-snackbar {
  background-color: #ff9800 !important;
  color: white !important;
}

::ng-deep .error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}
