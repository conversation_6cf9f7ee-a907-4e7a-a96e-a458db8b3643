import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { 
  StoredProcedure, 
  ProcedureParameter, 
  ExecutionResult, 
  ExecutionSummary,
  ParameterMapping 
} from '../models/stored-procedure.model';

@Injectable({
  providedIn: 'root'
})
export class StoredProcedureService {
  private baseUrl = 'api/stored-procedures'; // Replace with your actual API URL

  constructor(private http: HttpClient) { }

  // Search for stored procedures
  searchProcedures(searchTerm: string): Observable<StoredProcedure[]> {
    // For demo purposes, returning mock data
    // Replace this with actual HTTP call to your backend
    const mockProcedures: StoredProcedure[] = [
      {
        id: 1,
        name: 'sp_GetCustomers',
        schema: 'dbo',
        fullName: 'dbo.sp_GetCustomers',
        description: 'Retrieves customer information',
        created: new Date('2023-01-15'),
        modified: new Date('2023-06-20')
      },
      {
        id: 2,
        name: 'sp_UpdateCustomer',
        schema: 'dbo',
        fullName: 'dbo.sp_UpdateCustomer',
        description: 'Updates customer information',
        created: new Date('2023-02-10'),
        modified: new Date('2023-07-15')
      },
      {
        id: 3,
        name: 'sp_InsertOrder',
        schema: 'sales',
        fullName: 'sales.sp_InsertOrder',
        description: 'Inserts a new order',
        created: new Date('2023-03-05'),
        modified: new Date('2023-08-01')
      }
    ];

    const filteredProcedures = mockProcedures.filter(proc => 
      proc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      proc.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return of(filteredProcedures).pipe(delay(500)); // Simulate API delay

    // Actual implementation would be:
    // const params = new HttpParams().set('search', searchTerm);
    // return this.http.get<StoredProcedure[]>(`${this.baseUrl}/search`, { params });
  }

  // Get parameters for a specific stored procedure
  getProcedureParameters(procedureId: number): Observable<ProcedureParameter[]> {
    // Mock data for demo
    const mockParameters: { [key: number]: ProcedureParameter[] } = {
      1: [
        {
          id: 1,
          procedureId: 1,
          name: '@CustomerID',
          dataType: 'int',
          isOutput: false,
          isNullable: true,
          ordinalPosition: 1
        },
        {
          id: 2,
          procedureId: 1,
          name: '@CustomerName',
          dataType: 'varchar',
          maxLength: 100,
          isOutput: false,
          isNullable: true,
          ordinalPosition: 2
        }
      ],
      2: [
        {
          id: 3,
          procedureId: 2,
          name: '@CustomerID',
          dataType: 'int',
          isOutput: false,
          isNullable: false,
          ordinalPosition: 1
        },
        {
          id: 4,
          procedureId: 2,
          name: '@CustomerName',
          dataType: 'varchar',
          maxLength: 100,
          isOutput: false,
          isNullable: false,
          ordinalPosition: 2
        },
        {
          id: 5,
          procedureId: 2,
          name: '@Email',
          dataType: 'varchar',
          maxLength: 255,
          isOutput: false,
          isNullable: true,
          ordinalPosition: 3
        }
      ],
      3: [
        {
          id: 6,
          procedureId: 3,
          name: '@CustomerID',
          dataType: 'int',
          isOutput: false,
          isNullable: false,
          ordinalPosition: 1
        },
        {
          id: 7,
          procedureId: 3,
          name: '@ProductID',
          dataType: 'int',
          isOutput: false,
          isNullable: false,
          ordinalPosition: 2
        },
        {
          id: 8,
          procedureId: 3,
          name: '@Quantity',
          dataType: 'int',
          isOutput: false,
          isNullable: false,
          ordinalPosition: 3
        },
        {
          id: 9,
          procedureId: 3,
          name: '@OrderDate',
          dataType: 'datetime',
          isOutput: false,
          isNullable: true,
          defaultValue: 'GETDATE()',
          ordinalPosition: 4
        }
      ]
    };

    const parameters = mockParameters[procedureId] || [];
    return of(parameters).pipe(delay(300));

    // Actual implementation:
    // return this.http.get<ProcedureParameter[]>(`${this.baseUrl}/${procedureId}/parameters`);
  }

  // Execute stored procedure for multiple rows
  executeProcedureForRows(
    procedureId: number, 
    mappings: ParameterMapping[], 
    excelRows: any[][]
  ): Observable<ExecutionSummary> {
    // Mock execution for demo
    const results: ExecutionResult[] = [];
    let successCount = 0;
    let errorCount = 0;

    excelRows.forEach((row, index) => {
      const success = Math.random() > 0.1; // 90% success rate for demo
      const executionTime = Math.random() * 100 + 50; // Random execution time

      if (success) {
        successCount++;
        results.push({
          rowIndex: index,
          success: true,
          message: 'Procedure executed successfully',
          executionTime: executionTime,
          result: { affectedRows: 1 }
        });
      } else {
        errorCount++;
        results.push({
          rowIndex: index,
          success: false,
          error: 'Sample error: Invalid parameter value',
          executionTime: executionTime
        });
      }
    });

    const summary: ExecutionSummary = {
      totalRows: excelRows.length,
      successCount,
      errorCount,
      totalExecutionTime: results.reduce((sum, r) => sum + r.executionTime, 0),
      results
    };

    return of(summary).pipe(delay(2000)); // Simulate processing time

    // Actual implementation:
    // const payload = { procedureId, mappings, rows: excelRows };
    // return this.http.post<ExecutionSummary>(`${this.baseUrl}/execute-batch`, payload);
  }
}
