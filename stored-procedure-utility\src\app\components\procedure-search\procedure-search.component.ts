import { Component, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { StoredProcedure } from '../../models/stored-procedure.model';
import { StoredProcedureService } from '../../services/stored-procedure.service';

@Component({
  selector: 'app-procedure-search',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatIconModule,
    MatCardModule
  ],
  templateUrl: './procedure-search.component.html',
  styleUrls: ['./procedure-search.component.css']
})
export class ProcedureSearchComponent {
  @Output() procedureSelected = new EventEmitter<StoredProcedure>();

  searchTerm: string = '';
  procedures: StoredProcedure[] = [];
  selectedProcedure: StoredProcedure | null = null;
  isLoading: boolean = false;
  hasSearched: boolean = false;

  displayedColumns: string[] = ['name', 'schema', 'description', 'modified', 'actions'];

  constructor(private procedureService: StoredProcedureService) {}

  onSearch(): void {
    if (!this.searchTerm.trim()) {
      return;
    }

    this.isLoading = true;
    this.hasSearched = true;
    this.selectedProcedure = null;

    this.procedureService.searchProcedures(this.searchTerm).subscribe({
      next: (procedures) => {
        this.procedures = procedures;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error searching procedures:', error);
        this.procedures = [];
        this.isLoading = false;
      }
    });
  }

  onSelectProcedure(procedure: StoredProcedure): void {
    this.selectedProcedure = procedure;
    this.procedureSelected.emit(procedure);
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.onSearch();
    }
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.procedures = [];
    this.selectedProcedure = null;
    this.hasSearched = false;
    this.procedureSelected.emit(null as any);
  }
}
