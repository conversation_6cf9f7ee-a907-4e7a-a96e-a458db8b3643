/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Reference to a previously launched ripple element.
 */
export class RippleRef {
    constructor(_renderer, 
    /** Reference to the ripple HTML element. */
    element, 
    /** Ripple configuration used for the ripple. */
    config, 
    /* Whether animations are forcibly disabled for ripples through CSS. */
    _animationForciblyDisabledThroughCss = false) {
        this._renderer = _renderer;
        this.element = element;
        this.config = config;
        this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;
        /** Current state of the ripple. */
        this.state = 3 /* RippleState.HIDDEN */;
    }
    /** Fades out the ripple element. */
    fadeOut() {
        this._renderer.fadeOutRipple(this);
    }
}
//# sourceMappingURL=data:application/json;base64,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